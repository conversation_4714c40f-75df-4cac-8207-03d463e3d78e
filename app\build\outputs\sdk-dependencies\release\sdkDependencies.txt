# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.1"
  }
  digests {
    sha256: "*\3234\243#\262\200F\350\233s\214w\321\204\313=\314\243*U\032\260H\205\033/\332#\243\272&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.24"
  }
  digests {
    sha256: "\205\213\220&\226\332\234\365\205\253\235\230\377\301\302q\"i\202\203T\337\351\020~7\021\260\204\243dh"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.0"
  }
  digests {
    sha256: "\267\227\232z\254\224\005_\r\237\037\323\264|\345\377\341\313`2\250B\272\237\276q\206\360\205(\221x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.24"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.7"
  }
  digests {
    sha256: "Y\307A\020\271\223x\210^\320bB\370\242m\243\005\352M\312S\355`\0265@\335\016.<fu"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\370\351U\315\315\355d\005_\330\254;\342\226\017\032l\342}\'$\373\304Pu\234\360\321>\271\354\316"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.1"
  }
  digests {
    sha256: "\216-\263\022$\312S\261\b\307\204\332+6\031Y\006\'\026\324\026\262\020\317\357=Z8(0m\360"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.5.0"
  }
  digests {
    sha256: "\000\026/=\002D\244*\213\225\'\363\314o\263\2776\326U\226\fe\271y\276\362P\345\021o\024S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.biometric"
    artifactId: "biometric"
    version: "1.1.0"
  }
  digests {
    sha256: "\'\f{}\231\224-^\301\335\210YNFH\376\263=\2161\330\303\302\253#!\324\235\232\275\374\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.5.0"
  }
  digests {
    sha256: "q6DH[\034\264L\304\344\361\252v\251\203\\\337\252\370U\341\0279\270\262\\4\323\360k^\344"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.1.1"
  }
  digests {
    sha256: "\3271U\350>\227\207^\223cL\251\375\'\320\025\236\"\030\3634|S\256\252\360\372\242\357\252(\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "21.0.0"
  }
  digests {
    sha256: "\345\301\r\255\001\222\243wh\244\022e\005g\342\343E|p\222\260\r\330\301\036\311{\303o\217\250`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-blockstore"
    version: "16.4.0"
  }
  digests {
    sha256: "~p\362\f\250q\nx;\373\333&\2135`1\005\b\236U\023C\023\311w\345\272j\017\256I8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-identity-credentials"
    version: "16.0.0-alpha02"
  }
  digests {
    sha256: "<3Z\231\203\237\206\352\021\266\372\307\260\356\277`\375\304(\2608\3373&\3110\304\316bbPK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.1"
  }
  digests {
    sha256: "\002\231\374\016R\030\265\307f#\357\214\276\273\237\275\026M\003\355KH{A\215\351\243O+\351\215\271"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "32.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "22.3.0"
  }
  digests {
    sha256: "\336\370~\027\035\316y\203]\322L\217iu\021\370\236\027\200\230WDp/V\006\375\204\306!\3369"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.1.0"
  }
  digests {
    sha256: "\234A$}{\355\350R\016~C\251K\252\334\027M\355;\307\b}\017\2448-v\240\275\026\250Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.1.2"
  }
  digests {
    sha256: ";\247U\200\002k\206\002\034\325/_\252t\254n\305ca+{IW\246zt\305\037Z\036:\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "20.4.2"
  }
  digests {
    sha256: "7x.?6\033lBKT9\243\363=V\2243\327\342f\005X\001\365\037\374\240\272\335\250\230\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "17.1.5"
  }
  digests {
    sha256: "\344\3013\370\005[\030\224\201J\321i\002\216\027\366\262\024\203\215\vg\207&\215\316\375\300\235N\235\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "20.4.2"
  }
  digests {
    sha256: "\027\vDi\016H\r\035\336y\250\321cXj\020%\253UB\356\n\2548t\267n}\034\'q\217"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "24.10.0"
  }
  digests {
    sha256: "\244\341\247i a~\272\373\264mh\356\000J%`U\0333\204\355\364\361\177\f\302\2159\351eJ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.52.1"
  }
  digests {
    sha256: "H\351.\357\257\241\253(\365\320\025(\272\0066\025\272T\350Y\274d\240hrpss\212\205\"\266"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.52.1"
  }
  digests {
    sha256: "\235\361\215-\231FF\314m\330G\371\020\t\272\3339\020\211\250\262\000|\3531\204\350P\"Mc\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.52.1"
  }
  digests {
    sha256: "3\264\312\312m\"d\336Uh\020$r3u:\326\303`\303\323\037\254Y\312\002\343$\bL\363\366"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.52.1"
  }
  digests {
    sha256: "F\032\273\256\246\377&\312R%\321\251\305\222\243\273\342\246rH\262\006.\257\237P\323\032\017\350\213\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.9.0"
  }
  digests {
    sha256: "\311m`U\0231\241\226\332\305KtZ\246B\315\a\216\370\233o&qF\267\005\362\302\313\357\005-"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.21"
  }
  digests {
    sha256: "/%\204\034\223~$\225\232W\2660\342\304\270R[=\017So.Q\034\233+\3550\261e\035T"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.25.0"
  }
  digests {
    sha256: " DT)3\374\337@\255\030D\033\3547dm\025\fI\030q\025\177(\210G\342\234\270\035\344\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.52.1"
  }
  digests {
    sha256: "\265\223\327\341\303\b\370\324\307\241\215K_\317\033\226@-\020\245\323\324\322\352^\245\220\346\220\210\303\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "1.17.5"
  }
  digests {
    sha256: "\031\247\377H\330m<\364I\177\177%\017\277)_C\f\023\245(\335[{ ?\202\030\002\270\206\255"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.52.1"
  }
  digests {
    sha256: "\371@B[4\221%\220Y\355x\255F\370\375\311c\324\263\266\302_cx\177\376\002\344\246+\261\326"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.21.7"
  }
  digests {
    sha256: "C\224]y\036\316 \022\273\220\206\220~\022O\370\024\257\324\001B\t\366|\310\236\1772C\220\300a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.52.1"
  }
  digests {
    sha256: "\337\223\313\026Nt\263n\206\006\203%\244\"\207\362\211F\200N\n\361\251\250\352\217\254\022zl\376\r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.0"
  }
  digests {
    sha256: "\232\301ky[D\304\272\207\223{\240\250P&\r9?\235\302_\364i\325*{\255R\215\252\311\206"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "20.3.0"
  }
  digests {
    sha256: "wp<\333\226\204\034\"\257\\D{\'sv\347\232\353\032\037\301\204\343B\305\246x\202\270O\017s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "17.1.1"
  }
  digests {
    sha256: "\247\207\255oc\307\352u\316\242\207\324\346H\276HMk\227\036f^^g\364\270\304\220\325\335\246]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database"
    version: "20.3.0"
  }
  digests {
    sha256: "\256\276\347\357\317Cv\023\3446\347\003Eg\006\242u\200\354\367\025\374E\035\326\377\031\312\264\363\257\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.11.0"
  }
  digests {
    sha256: "\\)Nj_\017\201,\357\207k\204\022\225L\030\"\332\030J\363\216\b*[vn<OO\315\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.11.0"
  }
  digests {
    sha256: "\031z\034\325\267hU\252\002\2620\3019t\342\223\"\233\220\035\302\271o\253C\025 \036x\272\250\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.11.0"
  }
  digests {
    sha256: "\320gu\245\027\033wz\243\333\003\036\260\335J\035\276?\000\335\243[Ut\337\331S\366\260\325\357\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.11.0"
  }
  digests {
    sha256: "\322\031\3228\000m\202Ib\027b)\324p\212\274\335\334\3764,j\030\245\320\372H\326\360G\233>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.0.0"
  }
  digests {
    sha256: "\356H\276\020\252\270\365N\377\364\301Kw\321\036\020\271\356\356Cy\325\357k\362\227\242\222<U\314\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "3.14.9"
  }
  digests {
    sha256: "%p\372\265U\025\313\370\201\327\244\316\357I\374QT\220\274\002pW\346fwj(2FZ\354\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.PhilJay"
    artifactId: "MPAndroidChart"
    version: "3.1.0"
  }
  digests {
    sha256: "V\315\021<1\2330\034e\a\216\310\242o/\217g\252N\354\215i\023\311\360\n\326,5C\v\321"
  }
  repo_index {
    value: 2
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 46
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 9
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 43
  library_dep_index: 53
  library_dep_index: 39
  library_dep_index: 4
  library_dep_index: 46
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 45
  library_dep_index: 20
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 43
  library_dep_index: 39
  library_dep_index: 36
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 4
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 4
}
library_dependencies {
  library_index: 8
  library_dep_index: 4
}
library_dependencies {
  library_index: 9
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 4
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 36
  library_dep_index: 44
  library_dep_index: 4
  library_dep_index: 9
}
library_dependencies {
  library_index: 11
  library_dep_index: 4
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 14
  library_dep_index: 2
  library_dep_index: 15
}
library_dependencies {
  library_index: 16
  library_dep_index: 2
}
library_dependencies {
  library_index: 17
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 18
  library_dep_index: 2
}
library_dependencies {
  library_index: 19
  library_dep_index: 2
  library_dep_index: 18
}
library_dependencies {
  library_index: 20
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 6
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 5
  library_dep_index: 24
  library_dep_index: 8
  library_dep_index: 6
}
library_dependencies {
  library_index: 24
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 6
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 4
}
library_dependencies {
  library_index: 29
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 30
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 29
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 31
  library_dep_index: 29
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 32
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 34
  library_dep_index: 29
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 34
  library_dep_index: 2
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 2
}
library_dependencies {
  library_index: 37
  library_dep_index: 33
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 34
  library_dep_index: 29
}
library_dependencies {
  library_index: 38
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 4
}
library_dependencies {
  library_index: 40
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 33
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 12
}
library_dependencies {
  library_index: 43
  library_dep_index: 2
  library_dep_index: 14
  library_dep_index: 35
  library_dep_index: 15
}
library_dependencies {
  library_index: 44
  library_dep_index: 2
  library_dep_index: 12
}
library_dependencies {
  library_index: 45
  library_dep_index: 4
  library_dep_index: 4
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 0
}
library_dependencies {
  library_index: 47
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 12
}
library_dependencies {
  library_index: 48
  library_dep_index: 47
  library_dep_index: 16
  library_dep_index: 12
}
library_dependencies {
  library_index: 49
  library_dep_index: 2
}
library_dependencies {
  library_index: 50
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 42
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 51
  library_dep_index: 51
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 0
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 10
  library_dep_index: 50
  library_dep_index: 61
  library_dep_index: 11
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 66
  library_dep_index: 53
  library_dep_index: 69
  library_dep_index: 47
  library_dep_index: 68
}
library_dependencies {
  library_index: 55
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 57
  library_dep_index: 2
}
library_dependencies {
  library_index: 58
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 12
}
library_dependencies {
  library_index: 59
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 10
  library_dep_index: 43
}
library_dependencies {
  library_index: 60
  library_dep_index: 2
}
library_dependencies {
  library_index: 61
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 63
  library_dep_index: 40
  library_dep_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 63
  library_dep_index: 2
}
library_dependencies {
  library_index: 64
  library_dep_index: 2
}
library_dependencies {
  library_index: 65
  library_dep_index: 2
}
library_dependencies {
  library_index: 66
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 67
  library_dep_index: 9
  library_dep_index: 4
}
library_dependencies {
  library_index: 68
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 28
  library_dep_index: 66
}
library_dependencies {
  library_index: 69
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 61
}
library_dependencies {
  library_index: 70
  library_dep_index: 2
  library_dep_index: 71
  library_dep_index: 10
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 72
  library_dep_index: 4
}
library_dependencies {
  library_index: 71
  library_dep_index: 1
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 28
}
library_dependencies {
  library_index: 72
  library_dep_index: 70
  library_dep_index: 73
  library_dep_index: 78
  library_dep_index: 77
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 4
  library_dep_index: 70
  library_dep_index: 4
}
library_dependencies {
  library_index: 73
  library_dep_index: 28
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 77
  library_dep_index: 26
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 75
  library_dep_index: 12
  library_dep_index: 10
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 76
  library_dep_index: 12
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 77
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 7
  library_dep_index: 22
}
library_dependencies {
  library_index: 78
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 4
}
library_dependencies {
  library_index: 79
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 4
}
library_dependencies {
  library_index: 80
  library_dep_index: 70
  library_dep_index: 4
  library_dep_index: 6
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 93
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 115
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 64
  library_dep_index: 74
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 91
  library_dep_index: 4
}
library_dependencies {
  library_index: 83
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 10
  library_dep_index: 2
  library_dep_index: 15
}
library_dependencies {
  library_index: 84
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 85
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 84
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 25
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 88
  library_dep_index: 75
  library_dep_index: 26
}
library_dependencies {
  library_index: 89
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 86
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 25
  library_dep_index: 91
  library_dep_index: 86
  library_dep_index: 2
  library_dep_index: 14
  library_dep_index: 4
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 91
  library_dep_index: 86
  library_dep_index: 2
  library_dep_index: 56
}
library_dependencies {
  library_index: 92
  library_dep_index: 90
  library_dep_index: 6
  library_dep_index: 91
  library_dep_index: 86
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 111
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 2
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 86
  library_dep_index: 88
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 91
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
  library_dep_index: 99
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 56
  library_dep_index: 99
  library_dep_index: 106
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 56
  library_dep_index: 99
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
  library_dep_index: 15
  library_dep_index: 98
  library_dep_index: 101
  library_dep_index: 56
  library_dep_index: 102
}
library_dependencies {
  library_index: 107
  library_dep_index: 95
  library_dep_index: 108
  library_dep_index: 99
  library_dep_index: 106
}
library_dependencies {
  library_index: 109
  library_dep_index: 96
  library_dep_index: 110
  library_dep_index: 98
  library_dep_index: 99
}
library_dependencies {
  library_index: 111
  library_dep_index: 96
  library_dep_index: 99
  library_dep_index: 56
}
library_dependencies {
  library_index: 112
  library_dep_index: 75
}
library_dependencies {
  library_index: 113
  library_dep_index: 110
}
library_dependencies {
  library_index: 114
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 2
  library_dep_index: 75
  library_dep_index: 26
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 86
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 91
  library_dep_index: 115
}
library_dependencies {
  library_index: 115
  library_dep_index: 4
  library_dep_index: 2
  library_dep_index: 75
  library_dep_index: 26
  library_dep_index: 88
  library_dep_index: 92
}
library_dependencies {
  library_index: 116
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 91
  library_dep_index: 89
  library_dep_index: 112
  library_dep_index: 2
  library_dep_index: 75
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 4
  library_dep_index: 22
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 28
  library_dep_index: 48
  library_dep_index: 121
}
library_dependencies {
  library_index: 118
  library_dep_index: 2
}
library_dependencies {
  library_index: 121
  library_dep_index: 2
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
}
library_dependencies {
  library_index: 123
  library_dep_index: 108
}
library_dependencies {
  library_index: 124
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 54
  dependency_index: 1
  dependency_index: 59
  dependency_index: 70
  dependency_index: 72
  dependency_index: 80
  dependency_index: 81
  dependency_index: 82
  dependency_index: 116
  dependency_index: 93
  dependency_index: 114
  dependency_index: 66
  dependency_index: 117
  dependency_index: 32
  dependency_index: 37
  dependency_index: 122
  dependency_index: 124
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
