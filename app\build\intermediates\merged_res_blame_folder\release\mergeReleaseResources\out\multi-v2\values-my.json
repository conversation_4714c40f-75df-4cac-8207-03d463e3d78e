{"logs": [{"outputFile": "com.example.onlinecoffeeshop.app-mergeReleaseResources-46:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ab96aaedc8b77be230de629d335c4682\\transformed\\appcompat-1.7.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,14245", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,14326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3deef03585fdbcf847060ea76ca8493b\\transformed\\credentials-1.5.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3096,3208", "endColumns": "111,119", "endOffsets": "3203,3323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff6c67585aec0bbe59340a829427f899\\transformed\\play-services-basement-18.4.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5869", "endColumns": "153", "endOffsets": "6018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da31a2cc9a105a6c76a8d863f14fb806\\transformed\\core-1.15.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "40,41,42,43,44,45,46,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3779,3882,3986,4089,4191,4296,4402,14576", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3877,3981,4084,4186,4291,4397,4516,14672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6652059f755addc44c2f11488007a2c1\\transformed\\play-services-base-18.5.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4841,4948,5112,5246,5357,5504,5636,5759,6023,6199,6305,6475,6618,6776,6963,7033,7106", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "4943,5107,5241,5352,5499,5631,5754,5864,6194,6300,6470,6613,6771,6958,7028,7101,7190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9e6cd48de5ab9a20fa97ed71f349c2a\\transformed\\biometric-1.1.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,260,380,501,635,772,902,1055,1144,1294,1437", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "159,255,375,496,630,767,897,1050,1139,1289,1432,1569"}, "to": {"startLines": "68,70,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7195,7413,8055,8175,8296,8430,8567,8697,8850,8939,9089,9232", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "7299,7504,8170,8291,8425,8562,8692,8845,8934,9084,9227,9364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3247c3f3054ee99772ccb09043368b74\\transformed\\browser-1.4.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,106", "endOffsets": "159,265,380,487"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7304,7727,7833,7948", "endColumns": "108,105,114,106", "endOffsets": "7408,7828,7943,8050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\927157856c8a68fcd4c771cacd883cfc\\transformed\\material-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1119,1184,1274,1349,1408,1499,1562,1627,1686,1757,1819,1876,1995,2053,2114,2169,2242,2374,2465,2549,2649,2735,2824,2965,3043,3120,3243,3335,3412,3470,3521,3587,3659,3741,3812,3890,3965,4039,4111,4190,4298,4395,4476,4562,4654,4728,4807,4893,4947,5023,5091,5174,5255,5317,5381,5444,5512,5624,5735,5839,5952,6013,6068,6150,6237,6317", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "280,381,480,556,647,731,837,966,1051,1114,1179,1269,1344,1403,1494,1557,1622,1681,1752,1814,1871,1990,2048,2109,2164,2237,2369,2460,2544,2644,2730,2819,2960,3038,3115,3238,3330,3407,3465,3516,3582,3654,3736,3807,3885,3960,4034,4106,4185,4293,4390,4471,4557,4649,4723,4802,4888,4942,5018,5086,5169,5250,5312,5376,5439,5507,5619,5730,5834,5947,6008,6063,6145,6232,6312,6390"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,71,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3328,3429,3528,3604,3695,4521,4627,4756,7509,7572,7637,9369,9444,9503,9594,9657,9722,9781,9852,9914,9971,10090,10148,10209,10264,10337,10469,10560,10644,10744,10830,10919,11060,11138,11215,11338,11430,11507,11565,11616,11682,11754,11836,11907,11985,12060,12134,12206,12285,12393,12490,12571,12657,12749,12823,12902,12988,13042,13118,13186,13269,13350,13412,13476,13539,13607,13719,13830,13934,14047,14108,14163,14331,14418,14498", "endLines": "5,35,36,37,38,39,47,48,49,71,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "330,3424,3523,3599,3690,3774,4622,4751,4836,7567,7632,7722,9439,9498,9589,9652,9717,9776,9847,9909,9966,10085,10143,10204,10259,10332,10464,10555,10639,10739,10825,10914,11055,11133,11210,11333,11425,11502,11560,11611,11677,11749,11831,11902,11980,12055,12129,12201,12280,12388,12485,12566,12652,12744,12818,12897,12983,13037,13113,13181,13264,13345,13407,13471,13534,13602,13714,13825,13929,14042,14103,14158,14240,14413,14493,14571"}}]}]}