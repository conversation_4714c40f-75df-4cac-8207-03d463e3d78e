[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_favorite_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\favorite_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_bell_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\bell_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_splash_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\splash_pic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_favorite_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\favorite_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_btn_3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\btn_3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_btn_5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\btn_5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "com.example.onlinecoffeeshop.app-debug-48:/layout_activity_splash.xml.flat", "source": "com.example.onlinecoffeeshop.app-main-50:/layout/activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_btn_3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\btn_3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_back.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\back.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_btn_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\btn_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_btn_4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\btn_4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_item_product.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\item_product.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_splash_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\splash_pic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_star.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\star.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\menu_menu_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\menu\\menu_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_splash_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\splash_pic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_btn_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\btn_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_btn_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\btn_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_bell_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\bell_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_register.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_update_product.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_update_product.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_btn_4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\btn_4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_btn_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\btn_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_bell_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\bell_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_btn_3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\btn_3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_back.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\back.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_btn_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\btn_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_favorite_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\favorite_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_btn_3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\btn_3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_btn_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\btn_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_splash_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\splash_pic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_btn_5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\btn_5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_add_product.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_add_product.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_btn_5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\btn_5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_product_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_product_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_search_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\search_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_search_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\search_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_btn_3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\btn_3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_search_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\search_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_star.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\star.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_bell_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\bell_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_search_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\search_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_btn_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\btn_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_ic_coffee_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\ic_coffee_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_btn_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\btn_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_btn_4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\btn_4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_splash_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\splash_pic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_btn_3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\btn_3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_star.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\star.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_white_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\white_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_star.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\star.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_activity_product_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\activity_product_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_search_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\search_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_splash_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\splash_pic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_btn_5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\btn_5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_btn_5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\btn_5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_btn_5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\btn_5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_bell_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\bell_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_search_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\search_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_favorite_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\favorite_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_back.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\back.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_star.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\star.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_favorite_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\favorite_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_btn_4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\btn_4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_back.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\back.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_back.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\back.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-ldpi_btn_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-ldpi\\btn_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_favorite_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\favorite_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_btn_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\btn_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\layout_dialog_change_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\layout\\dialog_change_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxhdpi_star.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxhdpi\\star.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_back.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\back.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-hdpi_btn_4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-hdpi\\btn_4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable_readonly_field_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable\\readonly_field_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-mdpi_bell_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-mdpi\\bell_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_btn_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\btn_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xxxhdpi_btn_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xxxhdpi\\btn_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\drawable-xhdpi_btn_4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\drawable-xhdpi\\btn_4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-debug-48:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.onlinecoffeeshop.app-main-50:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}]