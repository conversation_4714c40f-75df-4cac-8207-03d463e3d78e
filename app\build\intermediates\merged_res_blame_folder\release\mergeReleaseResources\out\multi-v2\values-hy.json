{"logs": [{"outputFile": "com.example.onlinecoffeeshop.app-mergeReleaseResources-46:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6652059f755addc44c2f11488007a2c1\\transformed\\play-services-base-18.5.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4695,4801,4966,5100,5208,5362,5498,5625,5889,6056,6164,6332,6468,6630,6796,6861,6928", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "4796,4961,5095,5203,5357,5493,5620,5733,6051,6159,6327,6463,6625,6791,6856,6923,7005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\927157856c8a68fcd4c771cacd883cfc\\transformed\\material-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1044,1108,1205,1290,1352,1439,1501,1565,1626,1693,1754,1808,1930,1987,2047,2101,2182,2317,2401,2477,2567,2646,2731,2867,2942,3017,3160,3255,3335,3391,3444,3510,3584,3663,3734,3817,3888,3964,4040,4117,4223,4311,4391,4487,4583,4657,4735,4835,4886,4970,5039,5126,5217,5279,5343,5406,5477,5582,5688,5788,5891,5951,6008,6093,6176,6250", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "260,336,412,492,584,672,767,897,978,1039,1103,1200,1285,1347,1434,1496,1560,1621,1688,1749,1803,1925,1982,2042,2096,2177,2312,2396,2472,2562,2641,2726,2862,2937,3012,3155,3250,3330,3386,3439,3505,3579,3658,3729,3812,3883,3959,4035,4112,4218,4306,4386,4482,4578,4652,4730,4830,4881,4965,5034,5121,5212,5274,5338,5401,5472,5577,5683,5783,5886,5946,6003,6088,6171,6245,6325"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,71,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3257,3333,3409,3489,3581,4389,4484,4614,7316,7377,7441,9124,9209,9271,9358,9420,9484,9545,9612,9673,9727,9849,9906,9966,10020,10101,10236,10320,10396,10486,10565,10650,10786,10861,10936,11079,11174,11254,11310,11363,11429,11503,11582,11653,11736,11807,11883,11959,12036,12142,12230,12310,12406,12502,12576,12654,12754,12805,12889,12958,13045,13136,13198,13262,13325,13396,13501,13607,13707,13810,13870,13927,14095,14178,14252", "endLines": "5,35,36,37,38,39,47,48,49,71,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "310,3328,3404,3484,3576,3664,4479,4609,4690,7372,7436,7533,9204,9266,9353,9415,9479,9540,9607,9668,9722,9844,9901,9961,10015,10096,10231,10315,10391,10481,10560,10645,10781,10856,10931,11074,11169,11249,11305,11358,11424,11498,11577,11648,11731,11802,11878,11954,12031,12137,12225,12305,12401,12497,12571,12649,12749,12800,12884,12953,13040,13131,13193,13257,13320,13391,13496,13602,13702,13805,13865,13922,14007,14173,14247,14327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ab96aaedc8b77be230de629d335c4682\\transformed\\appcompat-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,14012", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,14090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da31a2cc9a105a6c76a8d863f14fb806\\transformed\\core-1.15.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "40,41,42,43,44,45,46,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3669,3769,3874,3972,4071,4176,4278,14332", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3764,3869,3967,4066,4171,4273,4384,14428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff6c67585aec0bbe59340a829427f899\\transformed\\play-services-basement-18.4.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5738", "endColumns": "150", "endOffsets": "5884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3247c3f3054ee99772ccb09043368b74\\transformed\\browser-1.4.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7124,7538,7641,7752", "endColumns": "103,102,110,102", "endOffsets": "7223,7636,7747,7850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3deef03585fdbcf847060ea76ca8493b\\transformed\\credentials-1.5.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,119", "endOffsets": "163,283"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3024,3137", "endColumns": "112,119", "endOffsets": "3132,3252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9e6cd48de5ab9a20fa97ed71f349c2a\\transformed\\biometric-1.1.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,257,372,498,621,752,871,1033,1136,1268,1403", "endColumns": "113,87,114,125,122,130,118,161,102,131,134,122", "endOffsets": "164,252,367,493,616,747,866,1028,1131,1263,1398,1521"}, "to": {"startLines": "68,70,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7010,7228,7855,7970,8096,8219,8350,8469,8631,8734,8866,9001", "endColumns": "113,87,114,125,122,130,118,161,102,131,134,122", "endOffsets": "7119,7311,7965,8091,8214,8345,8464,8626,8729,8861,8996,9119"}}]}]}