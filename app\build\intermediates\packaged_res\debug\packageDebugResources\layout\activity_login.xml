<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/background_color">

    <!-- Logo -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_coffee_logo"
        android:contentDescription="Coffee Shop Logo" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Đăng Nhập"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/primary_color"
        android:layout_marginBottom="32dp" />

    <!-- Email Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Email"
            android:inputType="textEmailAddress"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Password Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Mật khẩu"
            android:inputType="textPassword"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Forgot Password -->
    <TextView
        android:id="@+id/tv_forgot_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Quên mật khẩu?"
        android:textColor="@color/primary_color"
        android:textSize="14sp"
        android:layout_gravity="end"
        android:layout_marginBottom="24dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        android:padding="8dp" />

    <!-- Login Button -->
    <Button
        android:id="@+id/btn_login"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Đăng Nhập"
        android:textSize="16sp"
        android:textStyle="bold"
        android:backgroundTint="@color/primary_color"
        android:textColor="@android:color/white"
        android:layout_marginBottom="16dp" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <!-- Register Link -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chưa có tài khoản? "
            android:textSize="14sp"
            android:textColor="@color/text_secondary" />

        <TextView
            android:id="@+id/tv_register"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Đăng ký ngay"
            android:textColor="@color/primary_color"
            android:textSize="14sp"
            android:textStyle="bold"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground"
            android:padding="8dp" />

    </LinearLayout>

</LinearLayout>
