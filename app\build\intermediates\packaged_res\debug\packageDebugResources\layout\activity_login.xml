<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    android:background="@color/background_color">

    <!-- Logo -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_coffee_logo"
        android:contentDescription="Coffee Shop Logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_title"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Đăng Nhập"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/primary_color"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/til_email" />

    <!-- Email Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_email"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/til_password">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Email"
            android:inputType="textEmailAddress"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Password Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_password"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color"
        app:passwordToggleEnabled="true"
        app:layout_constraintTop_toBottomOf="@+id/til_email"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_forgot_password">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Mật khẩu"
            android:inputType="textPassword"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Forgot Password -->
    <TextView
        android:id="@+id/tv_forgot_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Quên mật khẩu?"
        android:textColor="@color/primary_color"
        android:textSize="14sp"
        android:layout_marginTop="16dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@+id/til_password"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_login" />

    <!-- Login Button -->
    <Button
        android:id="@+id/btn_login"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:text="Đăng Nhập"
        android:textSize="16sp"
        android:textStyle="bold"
        android:backgroundTint="@color/primary_color"
        android:textColor="@android:color/white"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_forgot_password"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_test_firebase" />

    <!-- Test Firebase Button -->
    <Button
        android:id="@+id/btn_test_firebase"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:text="🔥 Test Firebase Auth"
        android:textSize="14sp"
        android:backgroundTint="@color/secondary_color"
        android:textColor="@android:color/white"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_login"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_simple_test" />

    <!-- Basic Connection Test Button -->
    <Button
        android:id="@+id/btn_basic_test"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:text="🔧 Basic Test"
        android:textSize="14sp"
        android:backgroundTint="#4CAF50"
        android:textColor="@android:color/white"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_firebase"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_simple_test" />

    <!-- Simple Test Button -->
    <Button
        android:id="@+id/btn_simple_test"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:text="🚀 Simple Test"
        android:textSize="14sp"
        android:backgroundTint="#FF9800"
        android:textColor="@android:color/white"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_basic_test"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/progress_bar" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_login"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/ll_register" />

    <!-- Register Link -->
    <LinearLayout
        android:id="@+id/ll_register"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/progress_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chưa có tài khoản? "
            android:textSize="14sp"
            android:textColor="@color/text_secondary" />

        <TextView
            android:id="@+id/tv_register"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Đăng ký ngay"
            android:textColor="@color/primary_color"
            android:textSize="14sp"
            android:textStyle="bold"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground"
            android:padding="8dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
