# 🔥 Firebase Setup Guide - Fix Authentication Issues

## ⚠️ CRITICAL ISSUES IDENTIFIED:

### 1. **SHA-1 Fingerprint Missing in Firebase Console**
Your `google-services.json` shows `"oauth_client": []` - this means no SHA-1 fingerprint is registered.

**Your SHA-1 Fingerprint:**
```
B5:0B:23:DE:8B:4E:3D:7E:5A:3F:AC:2D:13:F4:E8:49:2B:19:83:41
```

### 2. **Anonymous Authentication Not Enabled**
The new simple test uses anonymous authentication which needs to be enabled.

---

## 🚀 IMMEDIATE FIXES NEEDED:

### **Step 1: Add SHA-1 to Firebase Console**
1. Go to https://console.firebase.google.com
2. Select project: `online-coffee-shop-3d7a2`
3. Go to **Project Settings** → **General** tab
4. Find your Android app: `com.example.onlinecoffeeshop`
5. Click **Add fingerprint**
6. Paste: `B5:0B:23:DE:8B:4E:3D:7E:5A:3F:AC:2D:13:F4:E8:49:2B:19:83:41`
7. Click **Save**

### **Step 2: Enable Authentication Methods**
1. In Firebase Console, go to **Authentication** → **Sign-in method**
2. Enable these providers:
   - ✅ **Email/Password** (should already be enabled)
   - ✅ **Anonymous** (click Enable)

### **Step 3: Download New google-services.json**
1. After adding SHA-1, go back to **Project Settings** → **General**
2. Click **Download google-services.json**
3. Replace the current file in your project: `app/google-services.json`

---

## 🧪 TESTING STEPS:

### **After completing Firebase Console setup:**

1. **Build the project:**
   ```bash
   ./gradlew assembleDebug -x lint
   ```

2. **Install and test:**
   - Install APK on device/emulator
   - Open LoginActivity
   - Click **"🚀 Simple Test"** button first
   - Should see: "✅ Firebase Auth hoạt động!"
   - Then test **"🔥 Test Firebase Auth"** button
   - Finally test actual login/register

3. **Check logs:**
   ```bash
   adb logcat | grep -E "(AuthController|LoginActivity)"
   ```

---

## 🔍 WHAT WAS FIXED:

### **Code Changes Made:**
1. ✅ **AndroidManifest.xml** - Added application class and network config
2. ✅ **Network Security Config** - Created for Firebase domains
3. ✅ **AuthController** - Added comprehensive logging and timeout handling
4. ✅ **Simple Test Method** - Anonymous auth test to verify Firebase connection
5. ✅ **Build Configuration** - Updated compileSdk to 36

### **Root Cause:**
- Missing SHA-1 fingerprint in Firebase Console
- Missing application class declaration in AndroidManifest
- Network security configuration needed for Firebase domains

---

## 📱 EXPECTED BEHAVIOR AFTER FIX:

### **Simple Test (🚀 Simple Test button):**
- Shows: "🔥 Testing Firebase Auth..."
- Should complete in 2-3 seconds
- Success: "✅ Firebase Auth hoạt động!"
- Failure: Shows specific error message

### **Login/Register:**
- Shows: "Đang đăng nhập..." immediately
- Should complete in 2-5 seconds
- Success: Navigate to MainActivity
- Failure: Shows specific error message
- Logs should appear in logcat

---

## 🆘 IF STILL NOT WORKING:

1. **Check internet connection**
2. **Verify SHA-1 was added correctly in Firebase Console**
3. **Ensure new google-services.json was downloaded and replaced**
4. **Clean and rebuild project:**
   ```bash
   ./gradlew clean
   ./gradlew assembleDebug
   ```
5. **Check logcat for specific error messages**

---

## 👥 FOR TEAM MEMBERS:

Each team member needs to:
1. Generate their own SHA-1: `./gradlew signingReport`
2. Add their SHA-1 to the same Firebase project
3. Everyone uses the same updated `google-services.json`

**Team SHA-1 Collection:**
- Member 1: `B5:0B:23:DE:8B:4E:3D:7E:5A:3F:AC:2D:13:F4:E8:49:2B:19:83:41` ✅
- Member 2: `[Add here]`
- Member 3: `[Add here]`
