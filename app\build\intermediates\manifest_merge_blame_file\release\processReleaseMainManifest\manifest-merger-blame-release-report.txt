1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
15    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
16    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
16-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
16-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
17
18    <permission
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:5-61:19
25        android:allowBackup="true"
25-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-65
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-54
30        android:icon="@mipmap/ic_launcher"
30-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-43
31        android:label="@string/app_name"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-54
33        android:supportsRtl="true"
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-35
34        android:theme="@style/Theme.OnlineCoffeeShop" >
34-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:9-54
35
36        <!-- Authentication Activities -->
37        <activity
37-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:9-29:20
38            android:name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
38-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:13-52
39            android:exported="true"
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-36
40            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" >
40-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:13-70
41            <intent-filter>
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:14-28:30
42                <action android:name="android.intent.action.MAIN" />
42-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:18-70
42-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:26-67
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:18-78
44-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:28-75
45            </intent-filter>
46        </activity>
47        <activity
47-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:30:9-33:73
48            android:name="com.example.onlinecoffeeshop.view.auth.RegisterActivity"
48-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:31:13-55
49            android:exported="false"
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:13-37
50            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" />
50-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:33:13-70
51
52        <!-- Profile Activity -->
53        <activity
53-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:36:9-39:58
54            android:name="com.example.onlinecoffeeshop.view.profile.ProfileActivity"
54-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:37:13-57
55            android:exported="false"
55-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:13-37
56            android:parentActivityName="com.example.onlinecoffeeshop.MainActivity" />
56-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:39:13-55
57
58        <!-- Product Activities -->
59        <activity
59-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:42:9-44:40
60            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
60-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:43:13-63
61            android:exported="false" />
61-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:13-37
62        <activity
62-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:9-47:40
63            android:name="com.example.onlinecoffeeshop.view.product.ProductDetailActivity"
63-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:46:13-63
64            android:exported="false" />
64-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:47:13-37
65        <activity
65-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:9-50:40
66            android:name="com.example.onlinecoffeeshop.view.product.ProductListActivity"
66-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-61
67            android:exported="false" />
67-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:13-37
68        <activity
68-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:9-53:40
69            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
69-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-60
70            android:exported="false" />
70-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:53:13-37
71
72        <!-- Main Activity -->
73        <activity
73-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:56:9-60:20
74            android:name="com.example.onlinecoffeeshop.MainActivity"
74-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:57:13-41
75            android:exported="true" >
75-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:58:13-36
76        </activity>
77        <activity
77-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
78            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
78-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
79            android:excludeFromRecents="true"
79-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
80            android:exported="true"
80-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
81            android:launchMode="singleTask"
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
82            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
82-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
83            <intent-filter>
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
84                <action android:name="android.intent.action.VIEW" />
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
85
86                <category android:name="android.intent.category.DEFAULT" />
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
87                <category android:name="android.intent.category.BROWSABLE" />
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
88
89                <data
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
90                    android:host="firebase.auth"
90-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
91                    android:path="/"
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
92                    android:scheme="genericidp" />
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
93            </intent-filter>
94        </activity>
95        <activity
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
96            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
97            android:excludeFromRecents="true"
97-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
98            android:exported="true"
98-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
99            android:launchMode="singleTask"
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
100            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
100-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
101            <intent-filter>
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
102                <action android:name="android.intent.action.VIEW" />
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
103
104                <category android:name="android.intent.category.DEFAULT" />
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
105                <category android:name="android.intent.category.BROWSABLE" />
105-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
105-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
106
107                <data
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
108                    android:host="firebase.auth"
108-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
109                    android:path="/"
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
110                    android:scheme="recaptcha" />
110-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
111            </intent-filter>
112        </activity>
113
114        <service
114-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
115            android:name="com.google.firebase.components.ComponentDiscoveryService"
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
116            android:directBootAware="true"
116-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
117            android:exported="false" >
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
118            <meta-data
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
119                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
121            <meta-data
121-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
122                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
122-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
124            <meta-data
124-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
125                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
125-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
127            <meta-data
127-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
128                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
130            <meta-data
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
131                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
131-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
133            <meta-data
133-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
134                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
134-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
136            <meta-data
136-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
137                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
137-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
139            <meta-data
139-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
140                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
140-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
142            <meta-data
142-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
143                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
143-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
145            <meta-data
145-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
146                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
146-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
148            <meta-data
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
149                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
151        </service>
152        <service
152-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
153            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
153-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
154            android:enabled="true"
154-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
155            android:exported="false" >
155-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
156            <meta-data
156-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
157                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
157-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
158                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
158-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
159        </service>
160
161        <activity
161-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
162            android:name="androidx.credentials.playservices.HiddenActivity"
162-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
163            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
163-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
164            android:enabled="true"
164-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
165            android:exported="false"
165-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
166            android:fitsSystemWindows="true"
166-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
167            android:theme="@style/Theme.Hidden" >
167-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
168        </activity>
169        <activity
169-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
170            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
170-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
171            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
171-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
172            android:enabled="true"
172-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
173            android:exported="false"
173-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
174            android:fitsSystemWindows="true"
174-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
175            android:theme="@style/Theme.Hidden" >
175-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
176        </activity>
177        <activity
177-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
178            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
178-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
179            android:excludeFromRecents="true"
179-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
180            android:exported="false"
180-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
181            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
181-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
182        <!--
183            Service handling Google Sign-In user revocation. For apps that do not integrate with
184            Google Sign-In, this service will never be started.
185        -->
186        <service
186-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
187            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
187-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
188            android:exported="true"
188-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
189            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
189-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
190            android:visibleToInstantApps="true" />
190-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
191
192        <provider
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
193            android:name="com.google.firebase.provider.FirebaseInitProvider"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
194            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
195            android:directBootAware="true"
195-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
196            android:exported="false"
196-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
197            android:initOrder="100" />
197-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
198        <provider
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
199            android:name="androidx.startup.InitializationProvider"
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
200            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
201            android:exported="false" >
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
202            <meta-data
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
203                android:name="androidx.emoji2.text.EmojiCompatInitializer"
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
204                android:value="androidx.startup" />
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
205            <meta-data
205-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
206                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
206-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
207                android:value="androidx.startup" />
207-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
208            <meta-data
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
209                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
210                android:value="androidx.startup" />
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
211        </provider>
212
213        <activity
213-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
214            android:name="com.google.android.gms.common.api.GoogleApiActivity"
214-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
215            android:exported="false"
215-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
216            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
216-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
217
218        <meta-data
218-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
219            android:name="com.google.android.gms.version"
219-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
220            android:value="@integer/google_play_services_version" />
220-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
221
222        <receiver
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
223            android:name="androidx.profileinstaller.ProfileInstallReceiver"
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
224            android:directBootAware="false"
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
225            android:enabled="true"
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
226            android:exported="true"
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
227            android:permission="android.permission.DUMP" >
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
228            <intent-filter>
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
229                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
230            </intent-filter>
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
232                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
233            </intent-filter>
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
235                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
238                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
239            </intent-filter>
240        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
241        <activity
241-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
242            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
242-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
243            android:exported="false"
243-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
244            android:stateNotNeeded="true"
244-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
245            android:theme="@style/Theme.PlayCore.Transparent" />
245-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
246    </application>
247
248</manifest>
