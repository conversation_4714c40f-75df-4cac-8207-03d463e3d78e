1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
15    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:5-61:19
24        android:allowBackup="true"
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-65
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-43
30        android:label="@string/app_name"
30-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-35
33        android:theme="@style/Theme.OnlineCoffeeShop" >
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:9-54
34
35        <!-- Authentication Activities -->
36        <activity
36-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:9-29:20
37            android:name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
37-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:13-52
38            android:exported="true"
38-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-36
39            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" >
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:13-70
40            <intent-filter>
40-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:14-28:30
41                <action android:name="android.intent.action.MAIN" />
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:18-70
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:26-67
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:18-78
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:28-75
44            </intent-filter>
45        </activity>
46        <activity
46-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:30:9-33:73
47            android:name="com.example.onlinecoffeeshop.view.auth.RegisterActivity"
47-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:31:13-55
48            android:exported="false"
48-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:13-37
49            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" />
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:33:13-70
50
51        <!-- Profile Activity -->
52        <activity
52-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:36:9-39:58
53            android:name="com.example.onlinecoffeeshop.view.profile.ProfileActivity"
53-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:37:13-57
54            android:exported="false"
54-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:13-37
55            android:parentActivityName="com.example.onlinecoffeeshop.MainActivity" />
55-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:39:13-55
56
57        <!-- Product Activities -->
58        <activity
58-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:42:9-44:40
59            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
59-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:43:13-63
60            android:exported="false" />
60-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:13-37
61        <activity
61-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:9-47:40
62            android:name="com.example.onlinecoffeeshop.view.product.ProductDetailActivity"
62-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:46:13-63
63            android:exported="false" />
63-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:47:13-37
64        <activity
64-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:9-50:40
65            android:name="com.example.onlinecoffeeshop.view.product.ProductListActivity"
65-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-61
66            android:exported="false" />
66-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:13-37
67        <activity
67-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:9-53:40
68            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
68-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-60
69            android:exported="false" />
69-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:53:13-37
70
71        <!-- Main Activity -->
72        <activity
72-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:56:9-60:20
73            android:name="com.example.onlinecoffeeshop.MainActivity"
73-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:57:13-41
74            android:exported="true" >
74-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:58:13-36
75        </activity>
76
77        <service
77-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
78            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
78-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
79            android:enabled="true"
79-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
80            android:exported="false" >
80-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
81            <meta-data
81-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
82                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
82-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
83                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
83-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
84        </service>
85
86        <activity
86-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
87            android:name="androidx.credentials.playservices.HiddenActivity"
87-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
88            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
88-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
89            android:enabled="true"
89-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
90            android:exported="false"
90-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
91            android:fitsSystemWindows="true"
91-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
92            android:theme="@style/Theme.Hidden" >
92-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
93        </activity>
94        <activity
94-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
95            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
95-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
96            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
96-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
97            android:enabled="true"
97-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
98            android:exported="false"
98-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
99            android:fitsSystemWindows="true"
99-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
100            android:theme="@style/Theme.Hidden" >
100-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
101        </activity>
102        <activity
102-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
103            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
103-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
104            android:excludeFromRecents="true"
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
105            android:exported="true"
105-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
106            android:launchMode="singleTask"
106-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
107            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
108            <intent-filter>
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
109                <action android:name="android.intent.action.VIEW" />
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
112                <category android:name="android.intent.category.BROWSABLE" />
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
113
114                <data
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
115                    android:host="firebase.auth"
115-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
116                    android:path="/"
116-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
117                    android:scheme="genericidp" />
117-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
118            </intent-filter>
119        </activity>
120        <activity
120-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
121            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
121-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
122            android:excludeFromRecents="true"
122-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
123            android:exported="true"
123-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
124            android:launchMode="singleTask"
124-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
125            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
126            <intent-filter>
126-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
127                <action android:name="android.intent.action.VIEW" />
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
131
132                <data
132-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
133                    android:host="firebase.auth"
133-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
134                    android:path="/"
134-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
135                    android:scheme="recaptcha" />
135-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
136            </intent-filter>
137        </activity>
138
139        <service
139-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
140            android:name="com.google.firebase.components.ComponentDiscoveryService"
140-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:67:13-84
141            android:directBootAware="true"
141-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
142            android:exported="false" >
142-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:68:13-37
143            <meta-data
143-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
144                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
144-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
146            <meta-data
146-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
147                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
147-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
149            <meta-data
149-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
150                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
150-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
152            <meta-data
152-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
153                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
153-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
155            <meta-data
155-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
156                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
156-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
158            <meta-data
158-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
159                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
159-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:30:17-120
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:31:17-82
161            <meta-data
161-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
162                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
162-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:33:17-109
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:34:17-82
164            <meta-data
164-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
165                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
165-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
167            <meta-data
167-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
168                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
168-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
170            <meta-data
170-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
171                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
171-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
173            <meta-data
173-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
174                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
174-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
176        </service>
177
178        <activity
178-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
179            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
179-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
180            android:excludeFromRecents="true"
180-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
181            android:exported="false"
181-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
182            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
182-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
183        <!--
184            Service handling Google Sign-In user revocation. For apps that do not integrate with
185            Google Sign-In, this service will never be started.
186        -->
187        <service
187-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
188            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
188-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
189            android:exported="true"
189-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
190            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
190-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
191            android:visibleToInstantApps="true" />
191-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
192
193        <activity
193-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
194            android:name="com.google.android.gms.common.api.GoogleApiActivity"
194-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
195            android:exported="false"
195-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
196            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
196-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
197
198        <provider
198-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
199            android:name="com.google.firebase.provider.FirebaseInitProvider"
199-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
200            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
200-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
201            android:directBootAware="true"
201-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
202            android:exported="false"
202-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
203            android:initOrder="100" />
203-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
204        <provider
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
205            android:name="androidx.startup.InitializationProvider"
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
206            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
207            android:exported="false" >
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
208            <meta-data
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
209                android:name="androidx.emoji2.text.EmojiCompatInitializer"
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
210                android:value="androidx.startup" />
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
211            <meta-data
211-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
212-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
213                android:value="androidx.startup" />
213-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
214            <meta-data
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
216                android:value="androidx.startup" />
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
217        </provider>
218
219        <meta-data
219-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
220            android:name="com.google.android.gms.version"
220-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
221            android:value="@integer/google_play_services_version" />
221-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
222
223        <receiver
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
224            android:name="androidx.profileinstaller.ProfileInstallReceiver"
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
225            android:directBootAware="false"
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
226            android:enabled="true"
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
227            android:exported="true"
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
228            android:permission="android.permission.DUMP" >
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
230                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
233                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
236                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
237            </intent-filter>
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
239                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
240            </intent-filter>
241        </receiver>
242    </application>
243
244</manifest>
