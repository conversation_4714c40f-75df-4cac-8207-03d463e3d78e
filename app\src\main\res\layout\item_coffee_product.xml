<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    android:backgroundTint="@color/coffee_brown">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Product Image -->
        <ImageView
            android:id="@+id/iv_product_image"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="12dp"
            android:scaleType="centerCrop"
            android:src="@drawable/cappuccino"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Product Name -->
        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Cappuccino"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/iv_product_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Product Description -->
        <TextView
            android:id="@+id/tv_product_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Espresso Milk"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:alpha="0.8"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Price and Add Button -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_description"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Price -->
            <TextView
                android:id="@+id/tv_product_price"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="$4.5"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Add Button -->
            <ImageView
                android:id="@+id/iv_add_to_cart"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/bg_add_button"
                android:padding="6dp"
                android:src="@drawable/ic_add"
                android:tint="@color/coffee_brown" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
