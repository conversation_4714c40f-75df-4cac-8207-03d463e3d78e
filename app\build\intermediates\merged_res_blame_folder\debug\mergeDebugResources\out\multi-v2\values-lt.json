{"logs": [{"outputFile": "com.example.onlinecoffeeshop.app-mergeDebugResources-45:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da31a2cc9a105a6c76a8d863f14fb806\\transformed\\core-1.15.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "42,43,44,45,46,47,48,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3847,3945,4055,4154,4257,4368,4478,14623", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3940,4050,4149,4252,4363,4473,4593,14719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9e6cd48de5ab9a20fa97ed71f349c2a\\transformed\\biometric-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,259,385,526,665,795,927,1064,1161,1316,1459", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "162,254,380,521,660,790,922,1059,1156,1311,1454,1576"}, "to": {"startLines": "70,72,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7162,7379,8024,8150,8291,8430,8560,8692,8829,8926,9081,9224", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "7269,7466,8145,8286,8425,8555,8687,8824,8921,9076,9219,9341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6652059f755addc44c2f11488007a2c1\\transformed\\play-services-base-18.5.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4896,5003,5155,5287,5394,5547,5677,5796,6061,6227,6336,6501,6635,6788,6938,7006,7072", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "4998,5150,5282,5389,5542,5672,5791,5897,6222,6331,6496,6630,6783,6933,7001,7067,7157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ab96aaedc8b77be230de629d335c4682\\transformed\\appcompat-1.7.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,14299", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,14378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\927157856c8a68fcd4c771cacd883cfc\\transformed\\material-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1160,1226,1325,1403,1468,1578,1641,1713,1772,1846,1907,1961,2085,2146,2208,2262,2340,2474,2562,2639,2732,2813,2897,3038,3117,3201,3344,3441,3518,3574,3628,3694,3769,3848,3919,3999,4075,4153,4226,4303,4410,4497,4578,4668,4760,4832,4913,5005,5060,5142,5208,5293,5380,5442,5506,5569,5641,5752,5868,5969,6078,6138,6196,6278,6364,6440", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1155,1221,1320,1398,1463,1573,1636,1708,1767,1841,1902,1956,2080,2141,2203,2257,2335,2469,2557,2634,2727,2808,2892,3033,3112,3196,3339,3436,3513,3569,3623,3689,3764,3843,3914,3994,4070,4148,4221,4298,4405,4492,4573,4663,4755,4827,4908,5000,5055,5137,5203,5288,5375,5437,5501,5564,5636,5747,5863,5964,6073,6133,6191,6273,6359,6435,6513"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,73,74,75,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3423,3502,3580,3663,3757,4598,4694,4812,7471,7534,7600,9346,9424,9489,9599,9662,9734,9793,9867,9928,9982,10106,10167,10229,10283,10361,10495,10583,10660,10753,10834,10918,11059,11138,11222,11365,11462,11539,11595,11649,11715,11790,11869,11940,12020,12096,12174,12247,12324,12431,12518,12599,12689,12781,12853,12934,13026,13081,13163,13229,13314,13401,13463,13527,13590,13662,13773,13889,13990,14099,14159,14217,14383,14469,14545", "endLines": "7,37,38,39,40,41,49,50,51,73,74,75,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,151,152,153", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "420,3497,3575,3658,3752,3842,4689,4807,4891,7529,7595,7694,9419,9484,9594,9657,9729,9788,9862,9923,9977,10101,10162,10224,10278,10356,10490,10578,10655,10748,10829,10913,11054,11133,11217,11360,11457,11534,11590,11644,11710,11785,11864,11935,12015,12091,12169,12242,12319,12426,12513,12594,12684,12776,12848,12929,13021,13076,13158,13224,13309,13396,13458,13522,13585,13657,13768,13884,13985,14094,14154,14212,14294,14464,14540,14618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff6c67585aec0bbe59340a829427f899\\transformed\\play-services-basement-18.4.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5902", "endColumns": "158", "endOffsets": "6056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3247c3f3054ee99772ccb09043368b74\\transformed\\browser-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "71,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7274,7699,7804,7918", "endColumns": "104,104,113,105", "endOffsets": "7374,7799,7913,8019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3deef03585fdbcf847060ea76ca8493b\\transformed\\credentials-1.5.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,113", "endOffsets": "165,279"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3194,3309", "endColumns": "114,113", "endOffsets": "3304,3418"}}]}]}