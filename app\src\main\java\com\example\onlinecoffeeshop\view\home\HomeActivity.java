package com.example.onlinecoffeeshop.view.home;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.onlinecoffeeshop.R;
import com.example.onlinecoffeeshop.adapter.CategoryAdapter;
import com.example.onlinecoffeeshop.adapter.HomeProductAdapter;
import com.example.onlinecoffeeshop.controller.AuthController;
import com.example.onlinecoffeeshop.model.Category;
import com.example.onlinecoffeeshop.model.Product;
import com.example.onlinecoffeeshop.view.auth.LoginActivity;
import com.example.onlinecoffeeshop.view.profile.ProfileActivity;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.card.MaterialCardView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HomeActivity extends AppCompatActivity {
    private static final String TAG = "HomeActivity";

    // Views
    private ImageView ivUserAvatar, ivNotification, ivFilter;
    private TextView tvUserName, tvSeeAll;
    private MaterialCardView cvPromotion;
    private RecyclerView rvCategories, rvPopularCoffees;
    private BottomNavigationView bottomNavigation;

    // Adapters
    private CategoryAdapter categoryAdapter;
    private HomeProductAdapter productAdapter;

    // Data
    private AuthController authController;
    private List<Category> categories;
    private List<Product> popularCoffees;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);

        initViews();
        initController();
        setupRecyclerViews();
        setupClickListeners();
        loadUserInfo();
        loadData();
    }

    private void initViews() {
        // Header views
        ivUserAvatar = findViewById(R.id.iv_user_avatar);
        tvUserName = findViewById(R.id.tv_user_name);
        ivNotification = findViewById(R.id.iv_notification);
        ivFilter = findViewById(R.id.iv_filter);

        // Promotion card
        cvPromotion = findViewById(R.id.cv_promotion);

        // RecyclerViews
        rvCategories = findViewById(R.id.rv_categories);
        rvPopularCoffees = findViewById(R.id.rv_popular_coffees);
        tvSeeAll = findViewById(R.id.tv_see_all);

        // Bottom navigation
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void initController() {
        authController = new AuthController(this);
    }

    private void setupRecyclerViews() {
        // Categories RecyclerView
        categories = new ArrayList<>();
        categoryAdapter = new CategoryAdapter(categories, this::onCategorySelected);
        rvCategories.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        rvCategories.setAdapter(categoryAdapter);

        // Popular Coffees RecyclerView
        popularCoffees = new ArrayList<>();
        productAdapter = new HomeProductAdapter(popularCoffees, this::onProductSelected);
        rvPopularCoffees.setLayoutManager(new GridLayoutManager(this, 2));
        rvPopularCoffees.setAdapter(productAdapter);
    }

    private void setupClickListeners() {
        // Header clicks
        ivUserAvatar.setOnClickListener(v -> openProfile());
        ivNotification.setOnClickListener(v -> openNotifications());
        ivFilter.setOnClickListener(v -> openFilter());

        // Promotion card click
        cvPromotion.setOnClickListener(v -> openPromotionDetails());

        // See all click
        tvSeeAll.setOnClickListener(v -> openAllProducts());

        // Bottom navigation
        bottomNavigation.setOnItemSelectedListener(item -> {
            int itemId = item.getItemId();
            if (itemId == R.id.nav_explorer) {
                return true;
            } else if (itemId == R.id.nav_cart) {
                openCart();
                return true;
            } else if (itemId == R.id.nav_wishlist) {
                openWishlist();
                return true;
            } else if (itemId == R.id.nav_my_order) {
                openMyOrders();
                return true;
            } else if (itemId == R.id.nav_profile) {
                openProfile();
                return true;
            }
            return false;
        });

        bottomNavigation.setSelectedItemId(R.id.nav_explorer);
    }

    private void loadUserInfo() {
        if (!authController.isUserLoggedIn()) {
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            finish();
            return;
        }

        authController.getCurrentUser(new AuthController.UserCallback() {
            @Override
            public void onSuccess(Map<String, Object> userData) {
                String fullname = (String) userData.get("fullname");
                if (fullname != null) {
                    tvUserName.setText(fullname);
                }
            }

            @Override
            public void onFailure(String error) {
                Log.e(TAG, "Failed to load user info: " + error);
                tvUserName.setText("User");
            }
        });
    }

    private void loadData() {
        loadCategories();
        loadPopularCoffees();
    }

    private void loadCategories() {
        categories.clear();
        categories.add(new Category("1", "Espresso", true));
        categories.add(new Category("2", "Cappuccino", false));
        categories.add(new Category("3", "Latte", false));
        categories.add(new Category("4", "American", false));
        categoryAdapter.notifyDataSetChanged();
    }

    private void loadPopularCoffees() {
        popularCoffees.clear();
        popularCoffees.add(new Product("1", "Cappuccino", "Espresso Milk", 4.5, R.drawable.cappuccino));
        popularCoffees.add(new Product("2", "Espresso", "Espresso", 3.5, R.drawable.espresso));
        popularCoffees.add(new Product("3", "Latte", "Milk Coffee", 5.0, R.drawable.latte));
        popularCoffees.add(new Product("4", "Americano", "Black Coffee", 3.0, R.drawable.americano));
        productAdapter.notifyDataSetChanged();
    }

    // Click handlers
    private void onCategorySelected(Category category) {
        for (Category cat : categories) {
            cat.setSelected(cat.getId().equals(category.getId()));
        }
        categoryAdapter.notifyDataSetChanged();
        filterProductsByCategory(category.getName());
    }

    private void onProductSelected(Product product) {
        Toast.makeText(this, "Selected: " + product.getName(), Toast.LENGTH_SHORT).show();
    }

    private void filterProductsByCategory(String categoryName) {
        Toast.makeText(this, "Filter by: " + categoryName, Toast.LENGTH_SHORT).show();
    }

    // Navigation methods
    private void openProfile() {
        Intent intent = new Intent(this, ProfileActivity.class);
        startActivity(intent);
    }

    private void openNotifications() {
        Toast.makeText(this, "Notifications", Toast.LENGTH_SHORT).show();
    }

    private void openFilter() {
        Toast.makeText(this, "Filter", Toast.LENGTH_SHORT).show();
    }

    private void openPromotionDetails() {
        Toast.makeText(this, "Promotion Details", Toast.LENGTH_SHORT).show();
    }

    private void openAllProducts() {
        Toast.makeText(this, "All Products", Toast.LENGTH_SHORT).show();
    }

    private void openCart() {
        Toast.makeText(this, "Cart", Toast.LENGTH_SHORT).show();
    }

    private void openWishlist() {
        Toast.makeText(this, "Wishlist", Toast.LENGTH_SHORT).show();
    }

    private void openMyOrders() {
        Toast.makeText(this, "My Orders", Toast.LENGTH_SHORT).show();
    }
}
