<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:padding="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/imgProduct"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_marginStart="10dp"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/txtName"
            android:textStyle="bold"
            android:textSize="18sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/txtPrice"
            android:textColor="#666"
            android:textSize="16sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <Button
            android:id="@+id/btnDelete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Xóa"
            android:backgroundTint="@android:color/holo_red_light"
            android:textColor="@android:color/white"
            android:layout_marginTop="4dp"
            />
    </LinearLayout>
</LinearLayout>
