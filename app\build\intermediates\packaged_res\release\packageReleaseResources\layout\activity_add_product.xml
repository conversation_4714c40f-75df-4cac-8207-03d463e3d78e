<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:padding="16dp"
    android:layout_height="match_parent">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <EditText android:id="@+id/edtName"
            android:hint="Product Name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <EditText android:id="@+id/edtCategoryId"
            android:hint="Category ID"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <EditText android:id="@+id/edtDescription"
            android:hint="Description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <EditText android:id="@+id/edtPrice"
            android:hint="Price"
            android:inputType="numberDecimal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <EditText android:id="@+id/edtImageUrl"
            android:hint="Image URL"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <CheckBox
            android:id="@+id/chkIsActive"
            android:text="Is Active"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btnAction"
            android:text="Add Product"
            android:layout_marginTop="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</ScrollView>
