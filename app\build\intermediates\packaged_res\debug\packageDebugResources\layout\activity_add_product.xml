<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Product Name -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Product Name"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/edtName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="e.g. Latte" />

        <!-- Category ID -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Category ID"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/edtCategoryId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="e.g. cat001" />

        <!-- Price -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Price (VND)"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/edtPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:hint="e.g. 45000" />

        <!-- Create Product Button -->
        <Button
            android:id="@+id/btnCreate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Add Product"
            android:backgroundTint="#6200EE"
            android:textColor="#FFFFFF" />
    </LinearLayout>
</ScrollView>
