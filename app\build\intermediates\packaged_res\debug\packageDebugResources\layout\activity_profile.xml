<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Avatar Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/circle_background"
                    android:src="@drawable/ic_person"
                    android:scaleType="centerCrop"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chạm để thay đổi ảnh"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- User Info Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/card_background"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thông tin tài khoản"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:layout_marginBottom="16dp" />

                <!-- Email (Read-only) -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Email"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:padding="12dp"
                    android:background="@drawable/readonly_field_background"
                    android:layout_marginBottom="16dp" />

                <!-- Role (Read-only) -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Vai trò"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_role"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Khách hàng"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:padding="12dp"
                    android:background="@drawable/readonly_field_background"
                    android:layout_marginBottom="16dp" />

            </LinearLayout>

            <!-- Editable Info Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/card_background"
                android:padding="16dp"
                android:layout_marginBottom="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thông tin cá nhân"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:layout_marginBottom="16dp" />

                <!-- Full Name Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_fullname"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Họ và tên"
                        android:inputType="textPersonName"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Date of Birth Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_dob"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Ngày sinh (dd/mm/yyyy)"
                        android:inputType="none"
                        android:focusable="false"
                        android:clickable="true"
                        android:textSize="16sp"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:drawablePadding="12dp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Address Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Địa chỉ"
                        android:inputType="textPostalAddress"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Phone Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Số điện thoại"
                        android:inputType="phone"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- Action Buttons -->
            <Button
                android:id="@+id/btn_update_profile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Cập nhật thông tin"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary_color"
                android:textColor="@android:color/white"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btn_change_password"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Đổi mật khẩu"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/secondary_color"
                android:textColor="@android:color/white"
                android:layout_marginBottom="16dp" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
