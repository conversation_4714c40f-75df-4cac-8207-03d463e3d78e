package com.example.onlinecoffeeshop.helper;

import android.text.TextUtils;
import android.util.Patterns;
import android.widget.EditText;

import java.util.regex.Pattern;

/**
 * Validator helper class for input validation
 * Contains all validation logic for authentication and user input
 */
public class Validator {

    // Password validation constants
    private static final int MIN_PASSWORD_LENGTH = 6;
    private static final int MAX_PASSWORD_LENGTH = 50;

    // Name validation constants
    private static final int MIN_NAME_LENGTH = 2;
    private static final int MAX_NAME_LENGTH = 50;

    // Phone validation constants
    private static final int MIN_PHONE_LENGTH = 10;
    private static final int MAX_PHONE_LENGTH = 15;

    // Regex patterns
    private static final Pattern PHONE_PATTERN = Pattern.compile("^[0-9+\\-\\s()]+$");
    private static final Pattern NAME_PATTERN = Pattern.compile("^[a-zA-ZÀ-ỹ\\s]+$");

    /**
     * Validate email address
     */
    public static ValidationResult validateEmail(String email) {
        if (TextUtils.isEmpty(email)) {
            return new ValidationResult(false, "Email không được để trống");
        }

        if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            return new ValidationResult(false, "Email không hợp lệ");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate email and set error to EditText if invalid
     */
    public static boolean validateEmail(EditText editText) {
        String email = editText.getText().toString().trim();
        ValidationResult result = validateEmail(email);

        if (!result.isValid()) {
            editText.setError(result.getErrorMessage());
            editText.requestFocus();
            return false;
        }

        editText.setError(null);
        return true;
    }

    /**
     * Validate password
     */
    public static ValidationResult validatePassword(String password) {
        if (TextUtils.isEmpty(password)) {
            return new ValidationResult(false, "Mật khẩu không được để trống");
        }

        if (password.length() < MIN_PASSWORD_LENGTH) {
            return new ValidationResult(false, "Mật khẩu phải có ít nhất " + MIN_PASSWORD_LENGTH + " ký tự");
        }

        if (password.length() > MAX_PASSWORD_LENGTH) {
            return new ValidationResult(false, "Mật khẩu không được quá " + MAX_PASSWORD_LENGTH + " ký tự");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate password and set error to EditText if invalid
     */
    public static boolean validatePassword(EditText editText) {
        String password = editText.getText().toString().trim();
        ValidationResult result = validatePassword(password);

        if (!result.isValid()) {
            editText.setError(result.getErrorMessage());
            editText.requestFocus();
            return false;
        }

        editText.setError(null);
        return true;
    }

    /**
     * Validate password confirmation
     */
    public static ValidationResult validatePasswordConfirmation(String password, String confirmPassword) {
        if (TextUtils.isEmpty(confirmPassword)) {
            return new ValidationResult(false, "Xác nhận mật khẩu không được để trống");
        }

        if (!password.equals(confirmPassword)) {
            return new ValidationResult(false, "Mật khẩu xác nhận không khớp");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate password confirmation and set error to EditText if invalid
     */
    public static boolean validatePasswordConfirmation(EditText passwordEditText, EditText confirmPasswordEditText) {
        String password = passwordEditText.getText().toString().trim();
        String confirmPassword = confirmPasswordEditText.getText().toString().trim();
        ValidationResult result = validatePasswordConfirmation(password, confirmPassword);

        if (!result.isValid()) {
            confirmPasswordEditText.setError(result.getErrorMessage());
            confirmPasswordEditText.requestFocus();
            return false;
        }

        confirmPasswordEditText.setError(null);
        return true;
    }

    /**
     * Validate full name
     */
    public static ValidationResult validateFullName(String fullName) {
        if (TextUtils.isEmpty(fullName)) {
            return new ValidationResult(false, "Họ tên không được để trống");
        }

        String trimmedName = fullName.trim();

        if (trimmedName.length() < MIN_NAME_LENGTH) {
            return new ValidationResult(false, "Họ tên phải có ít nhất " + MIN_NAME_LENGTH + " ký tự");
        }

        if (trimmedName.length() > MAX_NAME_LENGTH) {
            return new ValidationResult(false, "Họ tên không được quá " + MAX_NAME_LENGTH + " ký tự");
        }

        if (!NAME_PATTERN.matcher(trimmedName).matches()) {
            return new ValidationResult(false, "Họ tên chỉ được chứa chữ cái và khoảng trắng");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate full name and set error to EditText if invalid
     */
    public static boolean validateFullName(EditText editText) {
        String fullName = editText.getText().toString().trim();
        ValidationResult result = validateFullName(fullName);

        if (!result.isValid()) {
            editText.setError(result.getErrorMessage());
            editText.requestFocus();
            return false;
        }

        editText.setError(null);
        return true;
    }

    /**
     * Validate phone number
     */
    public static ValidationResult validatePhone(String phone) {
        if (TextUtils.isEmpty(phone)) {
            return new ValidationResult(false, "Số điện thoại không được để trống");
        }

        String trimmedPhone = phone.trim().replaceAll("\\s+", "");

        if (trimmedPhone.length() < MIN_PHONE_LENGTH) {
            return new ValidationResult(false, "Số điện thoại phải có ít nhất " + MIN_PHONE_LENGTH + " số");
        }

        if (trimmedPhone.length() > MAX_PHONE_LENGTH) {
            return new ValidationResult(false, "Số điện thoại không được quá " + MAX_PHONE_LENGTH + " số");
        }

        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return new ValidationResult(false, "Số điện thoại không hợp lệ");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate phone number and set error to EditText if invalid
     */
    public static boolean validatePhone(EditText editText) {
        String phone = editText.getText().toString().trim();
        ValidationResult result = validatePhone(phone);

        if (!result.isValid()) {
            editText.setError(result.getErrorMessage());
            editText.requestFocus();
            return false;
        }

        editText.setError(null);
        return true;
    }

    /**
     * Validate address
     */
    public static ValidationResult validateAddress(String address) {
        if (TextUtils.isEmpty(address)) {
            return new ValidationResult(false, "Địa chỉ không được để trống");
        }

        String trimmedAddress = address.trim();

        if (trimmedAddress.length() < 5) {
            return new ValidationResult(false, "Địa chỉ phải có ít nhất 5 ký tự");
        }

        if (trimmedAddress.length() > 200) {
            return new ValidationResult(false, "Địa chỉ không được quá 200 ký tự");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate address and set error to EditText if invalid
     */
    public static boolean validateAddress(EditText editText) {
        String address = editText.getText().toString().trim();
        ValidationResult result = validateAddress(address);

        if (!result.isValid()) {
            editText.setError(result.getErrorMessage());
            editText.requestFocus();
            return false;
        }

        editText.setError(null);
        return true;
    }

    /**
     * Validate date of birth
     */
    public static ValidationResult validateDateOfBirth(String dob) {
        if (TextUtils.isEmpty(dob)) {
            return new ValidationResult(false, "Ngày sinh không được để trống");
        }

        // Basic format validation (dd/MM/yyyy)
        if (!dob.matches("\\d{2}/\\d{2}/\\d{4}")) {
            return new ValidationResult(false, "Ngày sinh phải có định dạng dd/MM/yyyy");
        }

        return new ValidationResult(true, null);
    }

    /**
     * Validate date of birth and set error to EditText if invalid
     */
    public static boolean validateDateOfBirth(EditText editText) {
        String dob = editText.getText().toString().trim();
        ValidationResult result = validateDateOfBirth(dob);

        if (!result.isValid()) {
            editText.setError(result.getErrorMessage());
            editText.requestFocus();
            return false;
        }

        editText.setError(null);
        return true;
    }

    /**
     * Validate all login fields
     */
    public static boolean validateLoginFields(EditText emailEditText, EditText passwordEditText) {
        boolean isEmailValid = validateEmail(emailEditText);
        boolean isPasswordValid = validatePassword(passwordEditText);

        return isEmailValid && isPasswordValid;
    }

    /**
     * Validate all registration fields
     */
    public static boolean validateRegistrationFields(EditText fullNameEditText, EditText emailEditText,
                                                   EditText passwordEditText, EditText confirmPasswordEditText,
                                                   EditText dobEditText, EditText addressEditText,
                                                   EditText phoneEditText) {
        boolean isFullNameValid = validateFullName(fullNameEditText);
        boolean isEmailValid = validateEmail(emailEditText);
        boolean isPasswordValid = validatePassword(passwordEditText);
        boolean isPasswordConfirmValid = validatePasswordConfirmation(passwordEditText, confirmPasswordEditText);
        boolean isDobValid = validateDateOfBirth(dobEditText);
        boolean isAddressValid = validateAddress(addressEditText);
        boolean isPhoneValid = validatePhone(phoneEditText);

        return isFullNameValid && isEmailValid && isPasswordValid &&
               isPasswordConfirmValid && isDobValid && isAddressValid && isPhoneValid;
    }

    /**
     * ValidationResult inner class to hold validation results
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;

        public ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
