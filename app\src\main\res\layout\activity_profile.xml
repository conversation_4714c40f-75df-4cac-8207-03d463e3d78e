<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <!-- Avatar Section -->
            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:background="@drawable/circle_background"
                android:src="@drawable/ic_person"
                android:scaleType="centerCrop"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tv_avatar_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chạm để thay đổi ảnh"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toBottomOf="@+id/iv_avatar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- User Info Card -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/card_user_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@drawable/card_background"
                android:padding="16dp"
                android:layout_marginTop="32dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_avatar_hint"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:id="@+id/tv_account_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thông tin tài khoản"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <!-- Email Label -->
                <TextView
                    android:id="@+id/tv_email_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Email"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_account_title"
                    app:layout_constraintStart_toStartOf="parent" />

                <!-- Email Value -->
                <TextView
                    android:id="@+id/tv_email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:padding="12dp"
                    android:background="@drawable/readonly_field_background"
                    android:layout_marginTop="4dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_email_label"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <!-- Role Label -->
                <TextView
                    android:id="@+id/tv_role_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Vai trò"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_email"
                    app:layout_constraintStart_toStartOf="parent" />

                <!-- Role Value -->
                <TextView
                    android:id="@+id/tv_role"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Khách hàng"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:padding="12dp"
                    android:background="@drawable/readonly_field_background"
                    android:layout_marginTop="4dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_role_label"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Editable Info Card -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/card_editable_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@drawable/card_background"
                android:padding="16dp"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toBottomOf="@+id/card_user_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:id="@+id/tv_personal_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thông tin cá nhân"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <!-- Full Name Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_fullname"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:layout_constraintTop_toBottomOf="@+id/tv_personal_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_fullname"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Họ và tên"
                        android:inputType="textPersonName"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Date of Birth Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_dob"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:layout_constraintTop_toBottomOf="@+id/til_fullname"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_dob"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Ngày sinh (dd/mm/yyyy)"
                        android:inputType="none"
                        android:focusable="false"
                        android:clickable="true"
                        android:textSize="16sp"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:drawablePadding="12dp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Address Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:layout_constraintTop_toBottomOf="@+id/til_dob"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Địa chỉ"
                        android:inputType="textPostalAddress"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Phone Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:layout_constraintTop_toBottomOf="@+id/til_address"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Số điện thoại"
                        android:inputType="phone"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Action Buttons -->
            <Button
                android:id="@+id/btn_update_profile"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:text="Cập nhật thông tin"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary_color"
                android:textColor="@android:color/white"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@+id/card_editable_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <Button
                android:id="@+id/btn_change_password"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:text="Đổi mật khẩu"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/secondary_color"
                android:textColor="@android:color/white"
                android:layout_marginTop="12dp"
                app:layout_constraintTop_toBottomOf="@+id/btn_update_profile"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                app:layout_constraintTop_toBottomOf="@+id/btn_change_password"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
