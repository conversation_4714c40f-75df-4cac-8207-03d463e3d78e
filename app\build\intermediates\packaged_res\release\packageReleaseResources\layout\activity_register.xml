<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Title -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Đăng Ký"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Full Name Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_fullname"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_fullname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Họ và tên"
                android:inputType="textPersonName"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Email Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:layout_constraintTop_toBottomOf="@+id/til_fullname"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Email"
                android:inputType="textEmailAddress"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_password"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:passwordToggleEnabled="true"
            app:layout_constraintTop_toBottomOf="@+id/til_email"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Mật khẩu"
                android:inputType="textPassword"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Confirm Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_confirm_password"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:passwordToggleEnabled="true"
            app:layout_constraintTop_toBottomOf="@+id/til_password"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_confirm_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Xác nhận mật khẩu"
                android:inputType="textPassword"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Date of Birth Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_dob"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:layout_constraintTop_toBottomOf="@+id/til_confirm_password"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_dob"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Ngày sinh (dd/mm/yyyy)"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:textSize="16sp"
                android:drawableEnd="@drawable/ic_calendar"
                android:drawablePadding="12dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Address Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:layout_constraintTop_toBottomOf="@+id/til_dob"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Địa chỉ"
                android:inputType="textPostalAddress"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Phone Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            app:layout_constraintTop_toBottomOf="@+id/til_address"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Số điện thoại"
                android:inputType="phone"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Register Button -->
        <Button
            android:id="@+id/btn_register"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:text="Đăng Ký"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/primary_color"
            android:textColor="@android:color/white"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@+id/til_phone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@+id/btn_register"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Login Link -->
        <LinearLayout
            android:id="@+id/ll_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@+id/progress_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Đã có tài khoản? "
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Đăng nhập ngay"
                android:textColor="@color/primary_color"
                android:textSize="14sp"
                android:textStyle="bold"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground"
                android:padding="8dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
