{"logs": [{"outputFile": "com.example.onlinecoffeeshop.app-mergeReleaseResources-46:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da31a2cc9a105a6c76a8d863f14fb806\\transformed\\core-1.15.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "40,41,42,43,44,45,46,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3662,3757,3859,3956,4053,4159,4277,14127", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3752,3854,3951,4048,4154,4272,4387,14223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9e6cd48de5ab9a20fa97ed71f349c2a\\transformed\\biometric-1.1.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,260,375,504,642,776,905,1040,1140,1279,1416", "endColumns": "106,97,114,128,137,133,128,134,99,138,136,123", "endOffsets": "157,255,370,499,637,771,900,1035,1135,1274,1411,1535"}, "to": {"startLines": "68,70,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6898,7105,7734,7849,7978,8116,8250,8379,8514,8614,8753,8890", "endColumns": "106,97,114,128,137,133,128,134,99,138,136,123", "endOffsets": "7000,7198,7844,7973,8111,8245,8374,8509,8609,8748,8885,9009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\927157856c8a68fcd4c771cacd883cfc\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,71,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3253,3332,3408,3487,3577,4392,4498,4614,7203,7267,7332,9014,9079,9138,9225,9287,9349,9409,9475,9537,9591,9703,9760,9821,9875,9947,10073,10159,10237,10330,10416,10500,10639,10720,10801,10936,11026,11108,11161,11213,11279,11351,11435,11506,11586,11661,11737,11810,11885,11983,12068,12143,12235,12329,12403,12476,12570,12622,12704,12773,12858,12945,13007,13071,13134,13206,13309,13414,13509,13612,13669,13725,13890,13971,14049", "endLines": "5,35,36,37,38,39,47,48,49,71,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,149,150,151", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "314,3327,3403,3482,3572,3657,4493,4609,4692,7262,7327,7421,9074,9133,9220,9282,9344,9404,9470,9532,9586,9698,9755,9816,9870,9942,10068,10154,10232,10325,10411,10495,10634,10715,10796,10931,11021,11103,11156,11208,11274,11346,11430,11501,11581,11656,11732,11805,11880,11978,12063,12138,12230,12324,12398,12471,12565,12617,12699,12768,12853,12940,13002,13066,13129,13201,13304,13409,13504,13607,13664,13720,13800,13966,14044,14122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3247c3f3054ee99772ccb09043368b74\\transformed\\browser-1.4.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7005,7426,7524,7633", "endColumns": "99,97,108,100", "endOffsets": "7100,7519,7628,7729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff6c67585aec0bbe59340a829427f899\\transformed\\play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5703", "endColumns": "131", "endOffsets": "5830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3deef03585fdbcf847060ea76ca8493b\\transformed\\credentials-1.5.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3026,3135", "endColumns": "108,117", "endOffsets": "3130,3248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ab96aaedc8b77be230de629d335c4682\\transformed\\appcompat-1.7.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,13805", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,13885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6652059f755addc44c2f11488007a2c1\\transformed\\play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4697,4804,4968,5094,5200,5355,5482,5597,5835,6001,6106,6270,6396,6551,6695,6759,6819", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4799,4963,5089,5195,5350,5477,5592,5698,5996,6101,6265,6391,6546,6690,6754,6814,6893"}}]}]}