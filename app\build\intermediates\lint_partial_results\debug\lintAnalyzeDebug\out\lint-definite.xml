<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/model/User.java"
            line="45"
            column="63"
            startOffset="1198"
            endLine="45"
            endColumn="74"
            endOffset="1209"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/model/User.java"
            line="45"
            column="85"
            startOffset="1220"
            endLine="45"
            endColumn="96"
            endOffset="1231"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/model/User.java"
            line="56"
            column="63"
            startOffset="1664"
            endLine="56"
            endColumn="74"
            endOffset="1675"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/model/User.java"
            line="56"
            column="85"
            startOffset="1686"
            endLine="56"
            endColumn="96"
            endOffset="1697"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/model/User.java"
            line="100"
            column="63"
            startOffset="2719"
            endLine="100"
            endColumn="74"
            endOffset="2730"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/model/User.java"
            line="100"
            column="85"
            startOffset="2741"
            endLine="100"
            endColumn="96"
            endOffset="2752"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/repository/UserRepository.java"
            line="61"
            column="45"
            startOffset="2067"
            endLine="61"
            endColumn="56"
            endOffset="2078"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/repository/UserRepository.java"
            line="67"
            column="38"
            startOffset="2291"
            endLine="67"
            endColumn="49"
            endOffset="2302"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/repository/UserRepository.java"
            line="79"
            column="45"
            startOffset="2647"
            endLine="79"
            endColumn="56"
            endOffset="2658"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/repository/UserRepository.java"
            line="84"
            column="44"
            startOffset="2846"
            endLine="84"
            endColumn="55"
            endOffset="2857"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.1 is available: 8.11.0">
        <fix-replace
            description="Change to 8.11.0"
            family="Update versions"
            oldString="8.10.1"
            replacement="8.11.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="18"
            endLine="2"
            endColumn="15"
            endOffset="26"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="32.7.0"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="50"
            column="20"
            startOffset="1312"
            endLine="50"
            endColumn="71"
            endOffset="1363"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0">
        <fix-replace
            description="Change to 1.4.0"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="59"
            column="21"
            startOffset="1708"
            endLine="59"
            endColumn="63"
            endOffset="1750"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.github.bumptech.glide:glide than 4.11.0 is available: 4.12.0">
        <fix-replace
            description="Change to 4.12.0"
            family="Update versions"
            oldString="4.11.0"
            replacement="4.12.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="21"
            startOffset="1772"
            endLine="60"
            endColumn="61"
            endOffset="1812"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.6.2 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.2"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="61"
            column="21"
            startOffset="1834"
            endLine="61"
            endColumn="70"
            endOffset="1883"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.2 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.2"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="62"
            column="21"
            startOffset="1905"
            endLine="62"
            endColumn="71"
            endOffset="1955"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/view/product/ProductListActivity.java"
            line="65"
            column="17"
            startOffset="2404"
            endLine="65"
            endColumn="47"
            endOffset="2434"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_color` with a theme that also paints a background (inferred theme is `@style/Theme_OnlineCoffeeShop_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="7"
            column="5"
            startOffset="323"
            endLine="7"
            endColumn="49"
            endOffset="367"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_color` with a theme that also paints a background (inferred theme is `@style/Theme.OnlineCoffeeShop`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="6"
            column="5"
            startOffset="290"
            endLine="6"
            endColumn="49"
            endOffset="334"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_color` with a theme that also paints a background (inferred theme is `@style/Theme_OnlineCoffeeShop_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="6"
            column="5"
            startOffset="251"
            endLine="6"
            endColumn="49"
            endOffset="295"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="11"
            column="10"
            startOffset="348"
            endLine="11"
            endColumn="18"
            endOffset="356"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="16"
            column="10"
            startOffset="537"
            endLine="16"
            endColumn="18"
            endOffset="545"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="20"
            column="10"
            startOffset="729"
            endLine="20"
            endColumn="18"
            endOffset="737"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="31"
            column="10"
            startOffset="1154"
            endLine="31"
            endColumn="18"
            endOffset="1162"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="11"
            column="10"
            startOffset="348"
            endLine="11"
            endColumn="18"
            endOffset="356"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="16"
            column="10"
            startOffset="537"
            endLine="16"
            endColumn="18"
            endOffset="545"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="20"
            column="10"
            startOffset="729"
            endLine="20"
            endColumn="18"
            endOffset="737"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="25"
            column="10"
            startOffset="924"
            endLine="25"
            endColumn="18"
            endOffset="932"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="31"
            column="10"
            startOffset="1154"
            endLine="31"
            endColumn="18"
            endOffset="1162"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-firebase-bom"
            robot="true">
            <fix-replace
                description="Replace with firebaseBomVersion = &quot;32.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseBomVersion = &quot;32.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="28"
                    endOffset="28"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBomVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBomVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="401"
                    endOffset="401"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.firebase.bom"
                robot="true"
                replacement="libs.google.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1312"
                    endOffset="1363"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="50"
            column="20"
            startOffset="1312"
            endLine="50"
            endColumn="71"
            endOffset="1363"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-auth is already available as `firebase-auth`, but using version 23.2.1 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for com-google-firebase-firebase-auth"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with com-google-firebase-firebase-auth = { module = &quot;com.google.firebase:firebase-auth&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="com-google-firebase-firebase-auth = { module = &quot;com.google.firebase:firebase-auth&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="401"
                        endOffset="401"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.com.google.firebase.firebase.auth"
                    robot="true"
                    independent="true"
                    replacement="libs.com.google.firebase.firebase.auth"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="1452"
                        endOffset="1487"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-auth` (version 23.2.1)"
                replacement="libs.firebase.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1452"
                    endOffset="1487"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="53"
            column="20"
            startOffset="1452"
            endLine="53"
            endColumn="55"
            endOffset="1487"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-database is already available as `firebase-database`, but using version 21.0.0 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for com-google-firebase-firebase-database"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with com-google-firebase-firebase-database = { module = &quot;com.google.firebase:firebase-database&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="com-google-firebase-firebase-database = { module = &quot;com.google.firebase:firebase-database&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="401"
                        endOffset="401"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.com.google.firebase.firebase.database"
                    robot="true"
                    independent="true"
                    replacement="libs.com.google.firebase.firebase.database"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="1508"
                        endOffset="1547"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-database` (version 21.0.0)"
                replacement="libs.firebase.database"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1508"
                    endOffset="1547"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="54"
            column="20"
            startOffset="1508"
            endLine="54"
            endColumn="59"
            endOffset="1547"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-firestore is already available as `firebase-firestore`, but using version 25.1.4 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for com-google-firebase-firebase-firestore"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with com-google-firebase-firebase-firestore = { module = &quot;com.google.firebase:firebase-firestore&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="com-google-firebase-firebase-firestore = { module = &quot;com.google.firebase:firebase-firestore&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="401"
                        endOffset="401"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.com.google.firebase.firebase.firestore"
                    robot="true"
                    independent="true"
                    replacement="libs.com.google.firebase.firebase.firestore"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="1568"
                        endOffset="1608"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-firestore` (version 25.1.4)"
                replacement="libs.firebase.firestore"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1568"
                    endOffset="1608"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="55"
            column="20"
            startOffset="1568"
            endLine="55"
            endColumn="60"
            endOffset="1608"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-firebase-storage"
            robot="true">
            <fix-replace
                description="Replace with google-firebase-storage = { module = &quot;com.google.firebase:firebase-storage&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-firebase-storage = { module = &quot;com.google.firebase:firebase-storage&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="401"
                    endOffset="401"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.firebase.storage"
                robot="true"
                replacement="libs.google.firebase.storage"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1629"
                    endOffset="1667"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="56"
            column="20"
            startOffset="1629"
            endLine="56"
            endColumn="58"
            endOffset="1667"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-recyclerview"
            robot="true">
            <fix-replace
                description="Replace with recyclerviewVersion = &quot;1.3.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="recyclerviewVersion = &quot;1.3.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="386"
                    endOffset="386"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-recyclerview = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;recyclerviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-recyclerview = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;recyclerviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="401"
                    endOffset="401"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.recyclerview"
                robot="true"
                replacement="libs.androidx.recyclerview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1708"
                    endOffset="1750"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="59"
            column="21"
            startOffset="1708"
            endLine="59"
            endColumn="63"
            endOffset="1750"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for github-glide"
            robot="true">
            <fix-replace
                description="Replace with glideVersion = &quot;4.11.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="glideVersion = &quot;4.11.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="28"
                    endOffset="28"/>
            </fix-replace>
            <fix-replace
                description="Replace with github-glide = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;glideVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="github-glide = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;glideVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="401"
                    endOffset="401"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.github.glide"
                robot="true"
                replacement="libs.github.glide"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1772"
                    endOffset="1812"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="21"
            startOffset="1772"
            endLine="60"
            endColumn="61"
            endOffset="1812"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-lifecycle-livedata-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleLivedataKtxVersion = &quot;2.6.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleLivedataKtxVersion = &quot;2.6.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="115"
                    endOffset="115"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-lifecycle-livedata-ktx = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;lifecycleLivedataKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-lifecycle-livedata-ktx = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;lifecycleLivedataKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="401"
                    endOffset="401"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.lifecycle.livedata.ktx"
                robot="true"
                replacement="libs.androidx.lifecycle.livedata.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1834"
                    endOffset="1883"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="61"
            column="21"
            startOffset="1834"
            endLine="61"
            endColumn="70"
            endOffset="1883"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-lifecycle-viewmodel-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleViewmodelKtxVersion = &quot;2.6.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleViewmodelKtxVersion = &quot;2.6.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="115"
                    endOffset="115"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-lifecycle-viewmodel-ktx = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;lifecycleViewmodelKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-lifecycle-viewmodel-ktx = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;lifecycleViewmodelKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="401"
                    endOffset="401"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.lifecycle.viewmodel.ktx"
                robot="true"
                replacement="libs.androidx.lifecycle.viewmodel.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1905"
                    endOffset="1955"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="62"
            column="21"
            startOffset="1905"
            endLine="62"
            endColumn="71"
            endOffset="1955"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="386"
                    endOffset="386"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1786"
                    endOffset="1786"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1977"
                    endOffset="2016"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="63"
            column="21"
            startOffset="1977"
            endLine="63"
            endColumn="60"
            endOffset="2016"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for philjay-mpandroidchart"
            robot="true">
            <fix-replace
                description="Replace with mpandroidchartVersion = &quot;3.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="mpandroidchartVersion = &quot;3.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="386"
                    endOffset="386"/>
            </fix-replace>
            <fix-replace
                description="Replace with philjay-mpandroidchart = { module = &quot;com.github.PhilJay:MPAndroidChart&quot;, version.ref = &quot;mpandroidchartVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="philjay-mpandroidchart = { module = &quot;com.github.PhilJay:MPAndroidChart&quot;, version.ref = &quot;mpandroidchartVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1786"
                    endOffset="1786"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.philjay.mpandroidchart"
                robot="true"
                replacement="libs.philjay.mpandroidchart"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2070"
                    endOffset="2111"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="66"
            column="20"
            startOffset="2070"
            endLine="66"
            endColumn="61"
            endOffset="2111"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_product_detail.xml"
            line="11"
            column="10"
            startOffset="348"
            endLine="11"
            endColumn="19"
            endOffset="357"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="34"
            column="14"
            startOffset="1406"
            endLine="34"
            endColumn="23"
            endOffset="1415"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_product.xml"
            line="7"
            column="6"
            startOffset="230"
            endLine="7"
            endColumn="15"
            endOffset="239"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/adapter/ProductAdapter.java"
            line="44"
            column="30"
            startOffset="1522"
            endLine="44"
            endColumn="48"
            endOffset="1540"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/onlinecoffeeshop/view/product/ProductDetailActivity.java"
            line="55"
            column="26"
            startOffset="2033"
            endLine="55"
            endColumn="37"
            endOffset="2044"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Product Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="12"
            column="13"
            startOffset="396"
            endLine="12"
            endColumn="40"
            endOffset="423"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Category ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="17"
            column="13"
            startOffset="591"
            endLine="17"
            endColumn="39"
            endOffset="617"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="21"
            column="13"
            startOffset="784"
            endLine="21"
            endColumn="39"
            endOffset="810"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Price&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="26"
            column="13"
            startOffset="973"
            endLine="26"
            endColumn="33"
            endOffset="993"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Image URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="32"
            column="13"
            startOffset="1206"
            endLine="32"
            endColumn="37"
            endOffset="1230"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Is Active&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="38"
            column="13"
            startOffset="1409"
            endLine="38"
            endColumn="37"
            endOffset="1433"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Product&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_product.xml"
            line="44"
            column="13"
            startOffset="1608"
            endLine="44"
            endColumn="39"
            endOffset="1634"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Coffee Shop Logo&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="15"
            column="9"
            startOffset="575"
            endLine="15"
            endColumn="54"
            endOffset="620"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng Nhập&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="27"
            column="9"
            startOffset="1074"
            endLine="27"
            endColumn="33"
            endOffset="1098"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="54"
            column="13"
            startOffset="2290"
            endLine="54"
            endColumn="33"
            endOffset="2310"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mật khẩu&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="78"
            column="13"
            startOffset="3325"
            endLine="78"
            endColumn="36"
            endOffset="3348"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Quên mật khẩu?&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="89"
            column="9"
            startOffset="3692"
            endLine="89"
            endColumn="38"
            endOffset="3721"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng Nhập&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="106"
            column="9"
            startOffset="4357"
            endLine="106"
            endColumn="33"
            endOffset="4381"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Chưa có tài khoản? &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="144"
            column="13"
            startOffset="5953"
            endLine="144"
            endColumn="47"
            endOffset="5987"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng ký ngay&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="152"
            column="13"
            startOffset="6259"
            endLine="152"
            endColumn="40"
            endOffset="6286"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Product&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="25"
            column="9"
            startOffset="995"
            endLine="25"
            endColumn="35"
            endOffset="1021"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;View Product List&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="36"
            column="9"
            startOffset="1432"
            endLine="36"
            endColumn="41"
            endOffset="1464"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Update Product&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_product_detail.xml"
            line="41"
            column="13"
            startOffset="1425"
            endLine="41"
            endColumn="42"
            endOffset="1454"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add to Cart&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_product_detail.xml"
            line="47"
            column="13"
            startOffset="1676"
            endLine="47"
            endColumn="39"
            endOffset="1702"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Chạm để thay đổi ảnh&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="53"
            column="17"
            startOffset="2293"
            endLine="53"
            endColumn="52"
            endOffset="2328"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Thông tin tài khoản&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="77"
            column="21"
            startOffset="3472"
            endLine="77"
            endColumn="55"
            endOffset="3506"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="89"
            column="21"
            startOffset="4038"
            endLine="89"
            endColumn="41"
            endOffset="4058"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="101"
            column="21"
            startOffset="4601"
            endLine="101"
            endColumn="52"
            endOffset="4632"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Vai trò&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="116"
            column="21"
            startOffset="5365"
            endLine="116"
            endColumn="43"
            endOffset="5387"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Khách hàng&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="128"
            column="21"
            startOffset="5920"
            endLine="128"
            endColumn="46"
            endOffset="5945"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Thông tin cá nhân&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="157"
            column="21"
            startOffset="7377"
            endLine="157"
            endColumn="53"
            endOffset="7409"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Họ và tên&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="180"
            column="25"
            startOffset="8621"
            endLine="180"
            endColumn="49"
            endOffset="8645"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Ngày sinh (dd/mm/yyyy)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="202"
            column="25"
            startOffset="9749"
            endLine="202"
            endColumn="62"
            endOffset="9786"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Địa chỉ&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="228"
            column="25"
            startOffset="11099"
            endLine="228"
            endColumn="47"
            endOffset="11121"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Số điện thoại&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="251"
            column="25"
            startOffset="12291"
            endLine="251"
            endColumn="53"
            endOffset="12319"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cập nhật thông tin&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="264"
            column="17"
            startOffset="12775"
            endLine="264"
            endColumn="50"
            endOffset="12808"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đổi mật khẩu&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="278"
            column="17"
            startOffset="13437"
            endLine="278"
            endColumn="44"
            endOffset="13464"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng Ký&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="18"
            column="13"
            startOffset="664"
            endLine="18"
            endColumn="35"
            endOffset="686"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Họ và tên&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="43"
            column="17"
            startOffset="1819"
            endLine="43"
            endColumn="41"
            endOffset="1843"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="65"
            column="17"
            startOffset="2791"
            endLine="65"
            endColumn="37"
            endOffset="2811"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mật khẩu&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="88"
            column="17"
            startOffset="3812"
            endLine="88"
            endColumn="40"
            endOffset="3835"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Xác nhận mật khẩu&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="111"
            column="17"
            startOffset="4859"
            endLine="111"
            endColumn="49"
            endOffset="4891"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Ngày sinh (dd/mm/yyyy)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="133"
            column="17"
            startOffset="5849"
            endLine="133"
            endColumn="54"
            endOffset="5886"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Địa chỉ&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="159"
            column="17"
            startOffset="7015"
            endLine="159"
            endColumn="39"
            endOffset="7037"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Số điện thoại&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="181"
            column="17"
            startOffset="7987"
            endLine="181"
            endColumn="45"
            endOffset="8015"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng Ký&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="192"
            column="13"
            startOffset="8352"
            endLine="192"
            endColumn="35"
            endOffset="8374"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đã có tài khoản? &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="229"
            column="17"
            startOffset="9948"
            endLine="229"
            endColumn="49"
            endOffset="9980"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng nhập ngay&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="237"
            column="17"
            startOffset="10269"
            endLine="237"
            endColumn="46"
            endOffset="10298"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mật khẩu hiện tại&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_password.xml"
            line="22"
            column="13"
            startOffset="906"
            endLine="22"
            endColumn="45"
            endOffset="938"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mật khẩu mới&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_password.xml"
            line="41"
            column="13"
            startOffset="1671"
            endLine="41"
            endColumn="40"
            endOffset="1698"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Xác nhận mật khẩu mới&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_password.xml"
            line="59"
            column="13"
            startOffset="2396"
            endLine="59"
            endColumn="49"
            endOffset="2432"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Xóa&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_product.xml"
            line="38"
            column="13"
            startOffset="1265"
            endLine="38"
            endColumn="31"
            endOffset="1283"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Đăng xuất&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_profile.xml"
            line="7"
            column="9"
            startOffset="224"
            endLine="7"
            endColumn="34"
            endOffset="249"/>
    </incident>

</incidents>
