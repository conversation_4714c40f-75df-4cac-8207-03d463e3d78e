1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
13-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
13-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
14    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
15    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
15-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
15-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:5:5-30:19
24        android:allowBackup="true"
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:8:9-54
30        android:icon="@mipmap/ic_launcher"
30-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:9-43
31        android:label="@string/app_name"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-54
33        android:supportsRtl="true"
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.OnlineCoffeeShop" >
35-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-54
36        <activity
36-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-17:40
37            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
37-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:13-63
38            android:exported="false" />
38-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:13-37
39        <activity
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:18:9-20:40
40            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
40-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:19:13-60
41            android:exported="false" />
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:13-37
42        <activity
42-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:9-29:20
43            android:name="com.example.onlinecoffeeshop.MainActivity"
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-41
44            android:exported="true" >
44-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:13-36
45            <intent-filter>
45-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:13-28:29
46                <action android:name="android.intent.action.MAIN" />
46-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:17-69
46-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:17-77
48-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:27-74
49            </intent-filter>
50        </activity>
51        <activity
51-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
52            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
52-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
53            android:excludeFromRecents="true"
53-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
54            android:exported="true"
54-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
55            android:launchMode="singleTask"
55-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
56            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
56-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
57            <intent-filter>
57-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
58                <action android:name="android.intent.action.VIEW" />
58-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
58-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
59
60                <category android:name="android.intent.category.DEFAULT" />
60-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
60-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
61                <category android:name="android.intent.category.BROWSABLE" />
61-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
61-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
62
63                <data
63-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
64                    android:host="firebase.auth"
64-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
65                    android:path="/"
65-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
66                    android:scheme="genericidp" />
66-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
67            </intent-filter>
68        </activity>
69        <activity
69-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
70            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
70-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
71            android:excludeFromRecents="true"
71-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
72            android:exported="true"
72-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
73            android:launchMode="singleTask"
73-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
74            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
74-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
75            <intent-filter>
75-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
76                <action android:name="android.intent.action.VIEW" />
76-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
76-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
77
78                <category android:name="android.intent.category.DEFAULT" />
78-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
78-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
79                <category android:name="android.intent.category.BROWSABLE" />
79-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
79-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
80
81                <data
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
82                    android:host="firebase.auth"
82-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
83                    android:path="/"
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
84                    android:scheme="recaptcha" />
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
85            </intent-filter>
86        </activity>
87
88        <service
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
89            android:name="com.google.firebase.components.ComponentDiscoveryService"
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
90            android:directBootAware="true"
90-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
91            android:exported="false" >
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
92            <meta-data
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
93                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
93-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
95            <meta-data
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
96                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
98            <meta-data
98-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
99                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
99-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
101            <meta-data
101-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
102                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
102-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
104            <meta-data
104-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
105                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
105-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
107            <meta-data
107-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
108                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
108-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
110            <meta-data
110-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
111                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
111-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
113            <meta-data
113-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
114                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
114-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
116            <meta-data
116-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
117                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
117-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
119            <meta-data
119-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
120                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
120-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
122            <meta-data
122-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
123                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
123-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
125        </service>
126        <service
126-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
127            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
127-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
128            android:enabled="true"
128-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
129            android:exported="false" >
129-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
130            <meta-data
130-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
131                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
131-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
132                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
132-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
133        </service>
134
135        <activity
135-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
136            android:name="androidx.credentials.playservices.HiddenActivity"
136-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
137            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
137-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
138            android:enabled="true"
138-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
139            android:exported="false"
139-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
140            android:fitsSystemWindows="true"
140-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
141            android:theme="@style/Theme.Hidden" >
141-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
142        </activity>
143        <activity
143-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
144            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
144-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
145            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
145-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
146            android:enabled="true"
146-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
147            android:exported="false"
147-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
148            android:fitsSystemWindows="true"
148-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
149            android:theme="@style/Theme.Hidden" >
149-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
150        </activity>
151        <activity
151-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
152            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
152-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
153            android:excludeFromRecents="true"
153-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
154            android:exported="false"
154-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
155            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
155-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
156        <!--
157            Service handling Google Sign-In user revocation. For apps that do not integrate with
158            Google Sign-In, this service will never be started.
159        -->
160        <service
160-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
161            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
161-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
162            android:exported="true"
162-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
163            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
163-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
164            android:visibleToInstantApps="true" />
164-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
165
166        <provider
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
167            android:name="com.google.firebase.provider.FirebaseInitProvider"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
168            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
169            android:directBootAware="true"
169-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
170            android:exported="false"
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
171            android:initOrder="100" />
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
172        <provider
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
173            android:name="androidx.startup.InitializationProvider"
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
174            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
174-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
175            android:exported="false" >
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
176            <meta-data
176-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.emoji2.text.EmojiCompatInitializer"
177-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
178                android:value="androidx.startup" />
178-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
179            <meta-data
179-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
180-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
181                android:value="androidx.startup" />
181-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
183                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
184                android:value="androidx.startup" />
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
185        </provider>
186
187        <activity
187-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
188            android:name="com.google.android.gms.common.api.GoogleApiActivity"
188-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
189            android:exported="false"
189-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
190-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
191
192        <meta-data
192-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
193            android:name="com.google.android.gms.version"
193-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
194            android:value="@integer/google_play_services_version" />
194-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
195
196        <receiver
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
197            android:name="androidx.profileinstaller.ProfileInstallReceiver"
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
198            android:directBootAware="false"
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
199            android:enabled="true"
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
200            android:exported="true"
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
201            android:permission="android.permission.DUMP" >
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
203                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
206                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
207            </intent-filter>
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
209                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
212                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
213            </intent-filter>
214        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
215        <activity
215-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
216            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
216-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
217            android:exported="false"
217-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
218            android:stateNotNeeded="true"
218-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
219            android:theme="@style/Theme.PlayCore.Transparent" />
219-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
220    </application>
221
222</manifest>
