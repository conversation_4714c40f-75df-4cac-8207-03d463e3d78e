1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
13-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
14    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
15    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
16    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
16-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62599f99f9da15037b4f838385259657\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
16-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62599f99f9da15037b4f838385259657\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
17
18    <permission
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:8:5-60:19
25        android:allowBackup="true"
25-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-43
32        android:label="@string/app_name"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-54
34        android:supportsRtl="true"
34-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.OnlineCoffeeShop" >
36-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-54
37
38        <!-- Authentication Activities -->
39        <activity
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:19:9-28:20
40            android:name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
40-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:13-52
41            android:exported="true"
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:13-36
42            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" >
42-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-70
43            <intent-filter>
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:14-27:30
44                <action android:name="android.intent.action.MAIN" />
44-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:18-70
44-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:26-67
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:26:18-78
46-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:26:28-75
47            </intent-filter>
48        </activity>
49        <activity
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:29:9-32:73
50            android:name="com.example.onlinecoffeeshop.view.auth.RegisterActivity"
50-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:30:13-55
51            android:exported="false"
51-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:31:13-37
52            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" />
52-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:13-70
53
54        <!-- Profile Activity -->
55        <activity
55-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:35:9-38:58
56            android:name="com.example.onlinecoffeeshop.view.profile.ProfileActivity"
56-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:36:13-57
57            android:exported="false"
57-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:37:13-37
58            android:parentActivityName="com.example.onlinecoffeeshop.MainActivity" />
58-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:13-55
59
60        <!-- Product Activities -->
61        <activity
61-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:41:9-43:40
62            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
62-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:42:13-63
63            android:exported="false" />
63-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:43:13-37
64        <activity
64-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:9-46:40
65            android:name="com.example.onlinecoffeeshop.view.product.ProductDetailActivity"
65-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:13-63
66            android:exported="false" />
66-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:46:13-37
67        <activity
67-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:47:9-49:40
68            android:name="com.example.onlinecoffeeshop.view.product.ProductListActivity"
68-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:13-61
69            android:exported="false" />
69-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-37
70        <activity
70-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:9-52:40
71            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
71-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:13-60
72            android:exported="false" />
72-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-37
73
74        <!-- Main Activity -->
75        <activity
75-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:55:9-59:20
76            android:name="com.example.onlinecoffeeshop.MainActivity"
76-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:56:13-41
77            android:exported="true" >
77-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:57:13-36
78        </activity>
79        <activity
79-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
80            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
80-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
81            android:excludeFromRecents="true"
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
82            android:exported="true"
82-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
83            android:launchMode="singleTask"
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
84            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
85            <intent-filter>
85-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
86                <action android:name="android.intent.action.VIEW" />
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
89                <category android:name="android.intent.category.BROWSABLE" />
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
90
91                <data
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
92                    android:host="firebase.auth"
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
93                    android:path="/"
93-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
94                    android:scheme="genericidp" />
94-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
95            </intent-filter>
96        </activity>
97        <activity
97-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
98            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
98-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
99            android:excludeFromRecents="true"
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
100            android:exported="true"
100-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
101            android:launchMode="singleTask"
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
102            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
103            <intent-filter>
103-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
104                <action android:name="android.intent.action.VIEW" />
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
105
106                <category android:name="android.intent.category.DEFAULT" />
106-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
106-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
107                <category android:name="android.intent.category.BROWSABLE" />
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
108
109                <data
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
110                    android:host="firebase.auth"
110-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
111                    android:path="/"
111-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
112                    android:scheme="recaptcha" />
112-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
113            </intent-filter>
114        </activity>
115
116        <service
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
117            android:name="com.google.firebase.components.ComponentDiscoveryService"
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
118            android:directBootAware="true"
118-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
119            android:exported="false" >
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
120            <meta-data
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
121                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
123            <meta-data
123-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
124                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
124-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
126            <meta-data
126-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
127                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
127-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
129            <meta-data
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
130                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
132            <meta-data
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
133                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
133-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
135            <meta-data
135-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
136                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
136-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
138            <meta-data
138-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
139                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
139-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
141            <meta-data
141-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
142                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
142-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
144            <meta-data
144-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
145                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
145-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
147            <meta-data
147-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4550c1b9a3ff0c81c0d883c21ca5f69\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
148                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
148-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4550c1b9a3ff0c81c0d883c21ca5f69\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4550c1b9a3ff0c81c0d883c21ca5f69\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
150            <meta-data
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
151                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
153        </service>
154        <service
154-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
155            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
155-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
156            android:enabled="true"
156-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
157            android:exported="false" >
157-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
158            <meta-data
158-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
159                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
159-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
160                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
160-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
161        </service>
162
163        <activity
163-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
164            android:name="androidx.credentials.playservices.HiddenActivity"
164-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
165            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
165-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
166            android:enabled="true"
166-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
167            android:exported="false"
167-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
168            android:fitsSystemWindows="true"
168-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
169            android:theme="@style/Theme.Hidden" >
169-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
170        </activity>
171        <activity
171-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
172            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
172-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
173            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
173-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
174            android:enabled="true"
174-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
175            android:exported="false"
175-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
176            android:fitsSystemWindows="true"
176-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
177            android:theme="@style/Theme.Hidden" >
177-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
178        </activity>
179        <activity
179-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
180            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
180-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
181            android:excludeFromRecents="true"
181-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
182            android:exported="false"
182-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
183            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
183-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
184        <!--
185            Service handling Google Sign-In user revocation. For apps that do not integrate with
186            Google Sign-In, this service will never be started.
187        -->
188        <service
188-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
189            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
189-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
190            android:exported="true"
190-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
191            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
191-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
192            android:visibleToInstantApps="true" />
192-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
193
194        <provider
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
195            android:name="com.google.firebase.provider.FirebaseInitProvider"
195-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
196            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
196-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
197            android:directBootAware="true"
197-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
198            android:exported="false"
198-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
199            android:initOrder="100" />
199-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
200        <provider
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
201            android:name="androidx.startup.InitializationProvider"
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
202            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
203            android:exported="false" >
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
204            <meta-data
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
205                android:name="androidx.emoji2.text.EmojiCompatInitializer"
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
206                android:value="androidx.startup" />
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
207            <meta-data
207-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32cf5595d20b11d83c76cfb54f907991\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
208-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32cf5595d20b11d83c76cfb54f907991\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
209                android:value="androidx.startup" />
209-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32cf5595d20b11d83c76cfb54f907991\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
210            <meta-data
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
212                android:value="androidx.startup" />
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
213        </provider>
214
215        <activity
215-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
216            android:name="com.google.android.gms.common.api.GoogleApiActivity"
216-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
217            android:exported="false"
217-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
218            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
218-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
219
220        <meta-data
220-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b74234ee66916e86b3b52ea8b37045\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
221            android:name="com.google.android.gms.version"
221-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b74234ee66916e86b3b52ea8b37045\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
222            android:value="@integer/google_play_services_version" />
222-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b74234ee66916e86b3b52ea8b37045\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
223
224        <receiver
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
225            android:name="androidx.profileinstaller.ProfileInstallReceiver"
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
226            android:directBootAware="false"
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
227            android:enabled="true"
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
228            android:exported="true"
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
229            android:permission="android.permission.DUMP" >
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
230            <intent-filter>
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
231                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
232            </intent-filter>
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
234                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
237                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
240                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
241            </intent-filter>
242        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
243        <activity
243-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
244            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
244-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
245            android:exported="false"
245-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
246            android:stateNotNeeded="true"
246-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
247            android:theme="@style/Theme.PlayCore.Transparent" />
247-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
248    </application>
249
250</manifest>
