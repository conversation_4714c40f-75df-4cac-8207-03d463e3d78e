1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!-- Permissions -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
13-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
13-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
14    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
15    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
15-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
15-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:8:5-56:19
24        android:name="com.example.onlinecoffeeshop.CoffeeShopApplication"
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:9-46
25        android:allowBackup="true"
25-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-43
32        android:label="@string/app_name"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-54
34        android:supportsRtl="true"
34-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.OnlineCoffeeShop" >
36-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:9-54
37        <activity
37-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:19:9-27:20
38            android:name="com.example.onlinecoffeeshop.view.Splash"
38-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:13-40
39            android:exported="true" >
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:13-36
40            <intent-filter>
40-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-26:29
41                <action android:name="android.intent.action.MAIN" />
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:17-69
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:17-77
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:27-74
44            </intent-filter>
45        </activity>
46        <!-- Authentication Activities -->
47        <activity
47-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:29:9-32:72
48            android:name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
48-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:30:13-52
49            android:exported="true"
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:31:13-36
50            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" />
50-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:13-70
51        <activity
51-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:33:9-36:73
52            android:name="com.example.onlinecoffeeshop.view.auth.RegisterActivity"
52-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:34:13-55
53            android:exported="false"
53-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:35:13-37
54            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" /> <!-- Profile Activity -->
54-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:36:13-70
55        <activity
55-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:37:9-40:58
56            android:name="com.example.onlinecoffeeshop.view.profile.ProfileActivity"
56-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:13-57
57            android:exported="false"
57-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:39:13-37
58            android:parentActivityName="com.example.onlinecoffeeshop.MainActivity" /> <!-- Product Activities -->
58-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:40:13-55
59        <activity
59-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:41:9-43:40
60            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
60-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:42:13-63
61            android:exported="false" />
61-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:43:13-37
62        <activity
62-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:9-46:40
63            android:name="com.example.onlinecoffeeshop.view.product.ProductDetailActivity"
63-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:13-63
64            android:exported="false" />
64-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:46:13-37
65        <activity
65-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:47:9-49:40
66            android:name="com.example.onlinecoffeeshop.view.product.ProductListActivity"
66-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:13-61
67            android:exported="false" />
67-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-37
68        <activity
68-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:9-52:40
69            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
69-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:13-60
70            android:exported="false" /> <!-- Main Activity -->
70-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-37
71        <activity
71-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:53:9-55:48
72            android:name="com.example.onlinecoffeeshop.MainActivity"
72-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:54:13-41
73            android:exported="true" />
73-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:55:13-36
74        <activity
74-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
75            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
75-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
76            android:excludeFromRecents="true"
76-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
77            android:exported="true"
77-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
78            android:launchMode="singleTask"
78-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
79            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
79-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
80            <intent-filter>
80-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
81                <action android:name="android.intent.action.VIEW" />
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
82
83                <category android:name="android.intent.category.DEFAULT" />
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
84                <category android:name="android.intent.category.BROWSABLE" />
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
85
86                <data
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
87                    android:host="firebase.auth"
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
88                    android:path="/"
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
89                    android:scheme="genericidp" />
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
90            </intent-filter>
91        </activity>
92        <activity
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
93            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
93-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
94            android:excludeFromRecents="true"
94-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
95            android:exported="true"
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
96            android:launchMode="singleTask"
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
97            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
97-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
98            <intent-filter>
98-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
99                <action android:name="android.intent.action.VIEW" />
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
100
101                <category android:name="android.intent.category.DEFAULT" />
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
102                <category android:name="android.intent.category.BROWSABLE" />
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
103
104                <data
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
105                    android:host="firebase.auth"
105-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
106                    android:path="/"
106-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
107                    android:scheme="recaptcha" />
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
108            </intent-filter>
109        </activity>
110
111        <service
111-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
112            android:name="com.google.firebase.components.ComponentDiscoveryService"
112-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
113            android:directBootAware="true"
113-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
114            android:exported="false" >
114-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
115            <meta-data
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
116                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
118            <meta-data
118-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
119                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
119-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
121            <meta-data
121-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
122                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
122-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
124            <meta-data
124-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
125                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
125-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
127            <meta-data
127-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
128                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
130            <meta-data
130-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
131                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
131-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
133            <meta-data
133-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
134                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
134-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
136            <meta-data
136-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
137                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
137-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
139            <meta-data
139-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
140                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
140-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
142            <meta-data
142-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
143                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
143-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
145            <meta-data
145-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
146                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
146-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
148        </service>
149        <service
149-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
150            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
150-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
151            android:enabled="true"
151-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
152            android:exported="false" >
152-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
153            <meta-data
153-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
154                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
154-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
155                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
155-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
156        </service>
157
158        <activity
158-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
159            android:name="androidx.credentials.playservices.HiddenActivity"
159-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
160            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
160-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
161            android:enabled="true"
161-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
162            android:exported="false"
162-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
163            android:fitsSystemWindows="true"
163-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
164            android:theme="@style/Theme.Hidden" >
164-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
165        </activity>
166        <activity
166-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
167            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
167-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
168            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
168-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
169            android:enabled="true"
169-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
170            android:exported="false"
170-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
171            android:fitsSystemWindows="true"
171-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
172            android:theme="@style/Theme.Hidden" >
172-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
173        </activity>
174        <activity
174-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
175            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
175-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
176            android:excludeFromRecents="true"
176-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
177            android:exported="false"
177-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
178            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
178-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
179        <!--
180            Service handling Google Sign-In user revocation. For apps that do not integrate with
181            Google Sign-In, this service will never be started.
182        -->
183        <service
183-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
184            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
184-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
185            android:exported="true"
185-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
186            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
186-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
187            android:visibleToInstantApps="true" />
187-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
188
189        <provider
189-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
190            android:name="com.google.firebase.provider.FirebaseInitProvider"
190-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
191            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
191-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
192            android:directBootAware="true"
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
193            android:exported="false"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
194            android:initOrder="100" />
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
195        <provider
195-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
196            android:name="androidx.startup.InitializationProvider"
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
197            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
198            android:exported="false" >
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
199            <meta-data
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
200                android:name="androidx.emoji2.text.EmojiCompatInitializer"
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
201                android:value="androidx.startup" />
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
202            <meta-data
202-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
203                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
203-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
204                android:value="androidx.startup" />
204-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
205            <meta-data
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
206                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
207                android:value="androidx.startup" />
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
208        </provider>
209
210        <activity
210-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
211            android:name="com.google.android.gms.common.api.GoogleApiActivity"
211-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
212            android:exported="false"
212-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
213            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
213-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
214
215        <meta-data
215-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
216            android:name="com.google.android.gms.version"
216-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
217            android:value="@integer/google_play_services_version" />
217-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
218
219        <receiver
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
220            android:name="androidx.profileinstaller.ProfileInstallReceiver"
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
221            android:directBootAware="false"
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
222            android:enabled="true"
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
223            android:exported="true"
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
224            android:permission="android.permission.DUMP" >
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
226                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
227            </intent-filter>
228            <intent-filter>
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
229                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
230            </intent-filter>
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
232                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
233            </intent-filter>
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
235                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
236            </intent-filter>
237        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
238        <activity
238-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
239            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
239-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
240            android:exported="false"
240-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
241            android:stateNotNeeded="true"
241-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
242            android:theme="@style/Theme.PlayCore.Transparent" />
242-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
243    </application>
244
245</manifest>
