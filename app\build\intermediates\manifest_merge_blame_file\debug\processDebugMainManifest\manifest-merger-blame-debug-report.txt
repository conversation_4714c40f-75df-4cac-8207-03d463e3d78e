1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
13-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
14    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
15    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a5f0aaa24248907436e60f922a4aa5\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
16    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
16-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62599f99f9da15037b4f838385259657\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
16-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62599f99f9da15037b4f838385259657\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
17
18    <permission
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:8:5-63:19
25        android:name="com.example.onlinecoffeeshop.CoffeeShopApplication"
25-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:9-46
26        android:allowBackup="true"
26-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbc1e2e031c2dab80983bb3c95f79ee5\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-54
32        android:icon="@mipmap/ic_launcher"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-43
33        android:label="@string/app_name"
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-41
34        android:networkSecurityConfig="@xml/network_security_config"
34-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:18:9-69
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-54
36        android:supportsRtl="true"
36-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.OnlineCoffeeShop"
38-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:9-54
39        android:usesCleartextTraffic="true" >
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:19:9-44
40
41        <!-- Authentication Activities -->
42        <activity
42-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:9-31:20
43            android:name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:13-52
44            android:exported="true"
44-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:13-36
45            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" >
45-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:13-70
46            <intent-filter>
46-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:26:14-30:30
47                <action android:name="android.intent.action.MAIN" />
47-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:18-70
47-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:26-67
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:29:18-78
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:29:28-75
50            </intent-filter>
51        </activity>
52        <activity
52-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:9-35:73
53            android:name="com.example.onlinecoffeeshop.view.auth.RegisterActivity"
53-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:33:13-55
54            android:exported="false"
54-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:34:13-37
55            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" />
55-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:35:13-70
56
57        <!-- Profile Activity -->
58        <activity
58-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:9-41:58
59            android:name="com.example.onlinecoffeeshop.view.profile.ProfileActivity"
59-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:39:13-57
60            android:exported="false"
60-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:40:13-37
61            android:parentActivityName="com.example.onlinecoffeeshop.MainActivity" />
61-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:41:13-55
62
63        <!-- Product Activities -->
64        <activity
64-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:9-46:40
65            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
65-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:13-63
66            android:exported="false" />
66-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:46:13-37
67        <activity
67-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:47:9-49:40
68            android:name="com.example.onlinecoffeeshop.view.product.ProductDetailActivity"
68-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:13-63
69            android:exported="false" />
69-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-37
70        <activity
70-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:9-52:40
71            android:name="com.example.onlinecoffeeshop.view.product.ProductListActivity"
71-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:13-61
72            android:exported="false" />
72-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-37
73        <activity
73-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:53:9-55:40
74            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
74-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:54:13-60
75            android:exported="false" />
75-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:55:13-37
76
77        <!-- Main Activity -->
78        <activity
78-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:58:9-62:20
79            android:name="com.example.onlinecoffeeshop.MainActivity"
79-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:59:13-41
80            android:exported="true" >
80-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:60:13-36
81        </activity>
82        <activity
82-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
83            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
84            android:excludeFromRecents="true"
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
85            android:exported="true"
85-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
86            android:launchMode="singleTask"
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
87            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
88            <intent-filter>
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
89                <action android:name="android.intent.action.VIEW" />
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
90
91                <category android:name="android.intent.category.DEFAULT" />
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
92                <category android:name="android.intent.category.BROWSABLE" />
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
93
94                <data
94-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
95                    android:host="firebase.auth"
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
96                    android:path="/"
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
97                    android:scheme="genericidp" />
97-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
98            </intent-filter>
99        </activity>
100        <activity
100-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
101            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
102            android:excludeFromRecents="true"
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
103            android:exported="true"
103-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
104            android:launchMode="singleTask"
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
105            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
105-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
106            <intent-filter>
106-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
107                <action android:name="android.intent.action.VIEW" />
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
108
109                <category android:name="android.intent.category.DEFAULT" />
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
110                <category android:name="android.intent.category.BROWSABLE" />
110-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
110-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
111
112                <data
112-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
113                    android:host="firebase.auth"
113-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
114                    android:path="/"
114-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
115                    android:scheme="recaptcha" />
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
116            </intent-filter>
117        </activity>
118
119        <service
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
120            android:name="com.google.firebase.components.ComponentDiscoveryService"
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
121            android:directBootAware="true"
121-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
122            android:exported="false" >
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
123            <meta-data
123-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
124                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
124-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941bd2df935bc82d98d48915ab3b351f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
126            <meta-data
126-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
127                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
127-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
129            <meta-data
129-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
130                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
130-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afab6a599824fbd8173c55e30e3792bb\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
132            <meta-data
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
133                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
133-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
135            <meta-data
135-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
136                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
136-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c1ce633b464fbab03483bdb70de17c9\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
138            <meta-data
138-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
139                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
139-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
141            <meta-data
141-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
142                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
142-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2796d6bdf33ef0e28161f0add0844bb3\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
144            <meta-data
144-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
145                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
145-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
147            <meta-data
147-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
148                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
148-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784352c5051fe175be5bfeeb7dc7734\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
150            <meta-data
150-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4550c1b9a3ff0c81c0d883c21ca5f69\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
151                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
151-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4550c1b9a3ff0c81c0d883c21ca5f69\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4550c1b9a3ff0c81c0d883c21ca5f69\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
153            <meta-data
153-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
154                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
154-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
156        </service>
157        <service
157-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
158            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
158-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
159            android:enabled="true"
159-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
160            android:exported="false" >
160-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
161            <meta-data
161-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
162                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
162-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
163                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
163-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
164        </service>
165
166        <activity
166-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
167            android:name="androidx.credentials.playservices.HiddenActivity"
167-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
168            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
168-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
169            android:enabled="true"
169-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
170            android:exported="false"
170-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
171            android:fitsSystemWindows="true"
171-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
172            android:theme="@style/Theme.Hidden" >
172-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
173        </activity>
174        <activity
174-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
175            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
175-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
176            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
176-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
177            android:enabled="true"
177-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
178            android:exported="false"
178-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
179            android:fitsSystemWindows="true"
179-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
180            android:theme="@style/Theme.Hidden" >
180-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbc2b126a3ee5dc147089479d31350fa\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
181        </activity>
182        <activity
182-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
183            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
183-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
184            android:excludeFromRecents="true"
184-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
185            android:exported="false"
185-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
186            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
186-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
187        <!--
188            Service handling Google Sign-In user revocation. For apps that do not integrate with
189            Google Sign-In, this service will never be started.
190        -->
191        <service
191-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
192            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
192-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
193            android:exported="true"
193-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
194            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
194-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
195            android:visibleToInstantApps="true" />
195-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998d5e92967ba980915f3329a61f596c\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
196
197        <provider
197-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
198            android:name="com.google.firebase.provider.FirebaseInitProvider"
198-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
199            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
199-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
200            android:directBootAware="true"
200-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
201            android:exported="false"
201-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
202            android:initOrder="100" />
202-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e17067b80ab6a0d343bc7873b79856\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
203        <provider
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
204            android:name="androidx.startup.InitializationProvider"
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
205            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
206            android:exported="false" >
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
207            <meta-data
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.emoji2.text.EmojiCompatInitializer"
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
209                android:value="androidx.startup" />
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9c8a55616b0b6fcc654bebda366b358\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
210            <meta-data
210-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32cf5595d20b11d83c76cfb54f907991\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
211-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32cf5595d20b11d83c76cfb54f907991\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
212                android:value="androidx.startup" />
212-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32cf5595d20b11d83c76cfb54f907991\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
215                android:value="androidx.startup" />
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
216        </provider>
217
218        <activity
218-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
219            android:name="com.google.android.gms.common.api.GoogleApiActivity"
219-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
220            android:exported="false"
220-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
221-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a351c883281c926a1d36566bbf0a5f4\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
222
223        <meta-data
223-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b74234ee66916e86b3b52ea8b37045\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
224            android:name="com.google.android.gms.version"
224-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b74234ee66916e86b3b52ea8b37045\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
225            android:value="@integer/google_play_services_version" />
225-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b74234ee66916e86b3b52ea8b37045\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
226
227        <receiver
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
228            android:name="androidx.profileinstaller.ProfileInstallReceiver"
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
229            android:directBootAware="false"
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
230            android:enabled="true"
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
231            android:exported="true"
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
234                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
237                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
240                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
243                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25421a546da8d8f42b4ba10a55ccd70d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
244            </intent-filter>
245        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
246        <activity
246-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
247            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
247-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
248            android:exported="false"
248-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
249            android:stateNotNeeded="true"
249-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
250            android:theme="@style/Theme.PlayCore.Transparent" />
250-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\612deb786d0fd009318500a88950bc86\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
251    </application>
252
253</manifest>
