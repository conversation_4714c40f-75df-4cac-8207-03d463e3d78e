1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.onlinecoffeeshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
14-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
15    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:5-61:19
24        android:allowBackup="true"
24-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-54
30        android:icon="@mipmap/ic_launcher"
30-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-43
31        android:label="@string/app_name"
31-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-54
33        android:supportsRtl="true"
33-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.OnlineCoffeeShop" >
35-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:9-54
36
37        <!-- Authentication Activities -->
38        <activity
38-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:9-29:20
39            android:name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
39-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:13-52
40            android:exported="true"
40-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-36
41            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" >
41-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:13-70
42            <intent-filter>
42-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:14-28:30
43                <action android:name="android.intent.action.MAIN" />
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:18-70
43-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:26-67
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:18-78
45-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:28-75
46            </intent-filter>
47        </activity>
48        <activity
48-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:30:9-33:73
49            android:name="com.example.onlinecoffeeshop.view.auth.RegisterActivity"
49-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:31:13-55
50            android:exported="false"
50-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:13-37
51            android:theme="@style/Theme.OnlineCoffeeShop.NoActionBar" />
51-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:33:13-70
52
53        <!-- Profile Activity -->
54        <activity
54-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:36:9-39:58
55            android:name="com.example.onlinecoffeeshop.view.profile.ProfileActivity"
55-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:37:13-57
56            android:exported="false"
56-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:13-37
57            android:parentActivityName="com.example.onlinecoffeeshop.MainActivity" />
57-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:39:13-55
58
59        <!-- Product Activities -->
60        <activity
60-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:42:9-44:40
61            android:name="com.example.onlinecoffeeshop.view.product.UpdateProductActivity"
61-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:43:13-63
62            android:exported="false" />
62-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:13-37
63        <activity
63-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:9-47:40
64            android:name="com.example.onlinecoffeeshop.view.product.ProductDetailActivity"
64-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:46:13-63
65            android:exported="false" />
65-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:47:13-37
66        <activity
66-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:9-50:40
67            android:name="com.example.onlinecoffeeshop.view.product.ProductListActivity"
67-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-61
68            android:exported="false" />
68-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:13-37
69        <activity
69-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:9-53:40
70            android:name="com.example.onlinecoffeeshop.view.product.AddProductActivity"
70-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-60
71            android:exported="false" />
71-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:53:13-37
72
73        <!-- Main Activity -->
74        <activity
74-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:56:9-60:20
75            android:name="com.example.onlinecoffeeshop.MainActivity"
75-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:57:13-41
76            android:exported="true" >
76-->D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:58:13-36
77        </activity>
78
79        <service
79-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
80            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
80-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
81            android:enabled="true"
81-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
82            android:exported="false" >
82-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
83            <meta-data
83-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
84                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
84-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
85                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
85-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
86        </service>
87
88        <activity
88-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
89            android:name="androidx.credentials.playservices.HiddenActivity"
89-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
90            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
90-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
91            android:enabled="true"
91-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
92            android:exported="false"
92-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
93            android:fitsSystemWindows="true"
93-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
94            android:theme="@style/Theme.Hidden" >
94-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
95        </activity>
96        <activity
96-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
97            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
97-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
98            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
98-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
99            android:enabled="true"
99-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
100            android:exported="false"
100-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
101            android:fitsSystemWindows="true"
101-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
102            android:theme="@style/Theme.Hidden" >
102-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
103        </activity>
104        <activity
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
105            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
105-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
106            android:excludeFromRecents="true"
106-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
107            android:exported="true"
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
108            android:launchMode="singleTask"
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
109            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
110            <intent-filter>
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
111                <action android:name="android.intent.action.VIEW" />
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
112
113                <category android:name="android.intent.category.DEFAULT" />
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
114                <category android:name="android.intent.category.BROWSABLE" />
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
115
116                <data
116-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
117                    android:host="firebase.auth"
117-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
118                    android:path="/"
118-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
119                    android:scheme="genericidp" />
119-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
120            </intent-filter>
121        </activity>
122        <activity
122-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
123            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
123-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
124            android:excludeFromRecents="true"
124-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
125            android:exported="true"
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
126            android:launchMode="singleTask"
126-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
127            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
128            <intent-filter>
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
129                <action android:name="android.intent.action.VIEW" />
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
130
131                <category android:name="android.intent.category.DEFAULT" />
131-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
131-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
132                <category android:name="android.intent.category.BROWSABLE" />
132-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
132-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
133
134                <data
134-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
135                    android:host="firebase.auth"
135-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
136                    android:path="/"
136-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
137                    android:scheme="recaptcha" />
137-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
138            </intent-filter>
139        </activity>
140
141        <service
141-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
142            android:name="com.google.firebase.components.ComponentDiscoveryService"
142-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:67:13-84
143            android:directBootAware="true"
143-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
144            android:exported="false" >
144-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:68:13-37
145            <meta-data
145-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
146                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
146-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
148            <meta-data
148-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
149                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
149-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
151            <meta-data
151-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
152                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
152-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
154            <meta-data
154-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
155                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
155-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
157            <meta-data
157-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
158                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
158-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
160            <meta-data
160-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
161                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
161-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:30:17-120
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:31:17-82
163            <meta-data
163-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
164                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
164-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:33:17-109
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\AndroidManifest.xml:34:17-82
166            <meta-data
166-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
167                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
167-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
169            <meta-data
169-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
170                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
170-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
172            <meta-data
172-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
173                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
173-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
175            <meta-data
175-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
176                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
176-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
178        </service>
179
180        <activity
180-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
181            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
181-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
182            android:excludeFromRecents="true"
182-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
183            android:exported="false"
183-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
184            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
184-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
185        <!--
186            Service handling Google Sign-In user revocation. For apps that do not integrate with
187            Google Sign-In, this service will never be started.
188        -->
189        <service
189-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
190            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
190-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
191            android:exported="true"
191-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
192            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
192-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
193            android:visibleToInstantApps="true" />
193-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
194
195        <activity
195-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
196            android:name="com.google.android.gms.common.api.GoogleApiActivity"
196-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
197            android:exported="false"
197-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
198            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
198-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
199
200        <provider
200-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
201            android:name="com.google.firebase.provider.FirebaseInitProvider"
201-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
202            android:authorities="com.example.onlinecoffeeshop.firebaseinitprovider"
202-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
203            android:directBootAware="true"
203-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
204            android:exported="false"
204-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
205            android:initOrder="100" />
205-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
206        <provider
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
207            android:name="androidx.startup.InitializationProvider"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
208            android:authorities="com.example.onlinecoffeeshop.androidx-startup"
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
209            android:exported="false" >
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
210            <meta-data
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.emoji2.text.EmojiCompatInitializer"
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
212                android:value="androidx.startup" />
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
214-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
215                android:value="androidx.startup" />
215-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
216            <meta-data
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
217                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
218                android:value="androidx.startup" />
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
219        </provider>
220
221        <meta-data
221-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
222            android:name="com.google.android.gms.version"
222-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
223            android:value="@integer/google_play_services_version" />
223-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
224
225        <receiver
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
226            android:name="androidx.profileinstaller.ProfileInstallReceiver"
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
227            android:directBootAware="false"
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
228            android:enabled="true"
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
229            android:exported="true"
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
230            android:permission="android.permission.DUMP" >
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
232                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
233            </intent-filter>
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
235                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
238                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
241                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
242            </intent-filter>
243        </receiver>
244    </application>
245
246</manifest>
