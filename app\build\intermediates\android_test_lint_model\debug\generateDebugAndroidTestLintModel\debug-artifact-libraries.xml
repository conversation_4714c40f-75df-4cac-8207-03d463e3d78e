<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.test.ext:junit:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ffcba94fe766aed4b533a7c5478981b\transformed\junit-1.2.1\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ffcba94fe766aed4b533a7c5478981b\transformed\junit-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df9f197edf8396c0045769bb819b1072\transformed\espresso-core-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df9f197edf8396c0045769bb819b1072\transformed\espresso-core-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6ad1e95c77cb6c889557260421a538\transformed\appcompat-resources-1.7.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6ad1e95c77cb6c889557260421a538\transformed\appcompat-resources-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab96aaedc8b77be230de629d335c4682\transformed\appcompat-1.7.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab96aaedc8b77be230de629d335c4682\transformed\appcompat-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.11.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:22.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:22.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b91092646a9eae149112be99767f5af\transformed\firebase-auth-22.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3801390d2b5ecd2dd4690743c639f90\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3801390d2b5ecd2dd4690743c639f90\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7666bde154852e3b9810060b3a953de2\transformed\googleid-1.1.1\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7666bde154852e3b9810060b3a953de2\transformed\googleid-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3deef03585fdbcf847060ea76ca8493b\transformed\credentials-1.5.0\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3deef03585fdbcf847060ea76ca8493b\transformed\credentials-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:24.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:24.10.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11c8a336ba5eea0b42d62ec2fbdadad7\transformed\firebase-firestore-24.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database:20.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-database:20.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\778f1370c6eaac809399cd02bbf273b4\transformed\firebase-database-20.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:17.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33421d18db95a2aecb333acab891ec3\transformed\play-services-auth-api-phone-17.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:17.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f33421d18db95a2aecb333acab891ec3\transformed\play-services-auth-api-phone-17.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-storage:20.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-storage:20.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19bd819737d0970e5bed3d81b2bac963\transformed\recaptcha-18.1.2\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.1.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19bd819737d0970e5bed3d81b2bac963\transformed\recaptcha-18.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9157e6cf66f057a58fb7f5b66fa7502b\transformed\integrity-1.1.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9157e6cf66f057a58fb7f5b66fa7502b\transformed\integrity-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck:17.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:20.4.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf67e396c2914e120111fa97e35a7e90\transformed\firebase-common-ktx-20.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:20.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:20.4.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff1e167fd0ee8a5a96c4f003fd3d63fa\transformed\firebase-common-20.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f2b241146d22db375d6b4f8aaceca8\transformed\firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f2b241146d22db375d6b4f8aaceca8\transformed\firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5d917c2cd542a598049d81d4fe40fe\transformed\play-services-base-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5d917c2cd542a598049d81d4fe40fe\transformed\play-services-base-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41356a04cbc8b397e85a38eba1130e59\transformed\core-1.6.1\jars\classes.jar"
      resolved="androidx.test:core:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41356a04cbc8b397e85a38eba1130e59\transformed\core-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ae541c50319bb2e5eb82d03a1ce67ec\transformed\recyclerview-1.3.2\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ae541c50319bb2e5eb82d03a1ce67ec\transformed\recyclerview-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201989e88fc2ce9268d5ee71ec56015a\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201989e88fc2ce9268d5ee71ec56015a\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32fe6f491d0c652ec40c99e8c355842c\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32fe6f491d0c652ec40c99e8c355842c\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5418308e7f3d601ff183d1d3ef2a0b64\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5418308e7f3d601ff183d1d3ef2a0b64\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\425055bd0a74b18769344c92bfddf926\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\425055bd0a74b18769344c92bfddf926\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02600de27416b9134e507fddbbfbd334\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02600de27416b9134e507fddbbfbd334\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea628c60eede608044342426c52a971\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea628c60eede608044342426c52a971\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d621dd94924f45342c44c4da4705ad\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d621dd94924f45342c44c4da4705ad\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b95241c520638c422155228b404983f1\transformed\lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b95241c520638c422155228b404983f1\transformed\lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\123ac6967623a4f97269f1053d7be3bd\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\123ac6967623a4f97269f1053d7be3bd\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c0616fa7757653de67b32f10aea4bb8\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c0616fa7757653de67b32f10aea4bb8\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b81770157f9e38f553bbc16749e2cf6\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b81770157f9e38f553bbc16749e2cf6\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4aff8f7ec86942f01c117df75cf193b\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4aff8f7ec86942f01c117df75cf193b\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2c48b8b5d96d703fab7340ac903d3ab\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2c48b8b5d96d703fab7340ac903d3ab\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2119bdd70b1500056cd7daec66eeb2d1\transformed\core-ktx-1.13.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2119bdd70b1500056cd7daec66eeb2d1\transformed\core-ktx-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3247c3f3054ee99772ccb09043368b74\transformed\browser-1.4.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3247c3f3054ee99772ccb09043368b74\transformed\browser-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfdf836f4031be9c36703d49bea6653b\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfdf836f4031be9c36703d49bea6653b\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\370dc79520a49ee02f6105a4c76f3d16\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\370dc79520a49ee02f6105a4c76f3d16\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1c545c20c64d91156cc7060bab0e3b6\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1c545c20c64d91156cc7060bab0e3b6\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7baaf667d83bcc7fe6b2e65137ed0cae\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7baaf667d83bcc7fe6b2e65137ed0cae\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9bfda81c72d91dd55ac3f5c969251\transformed\lifecycle-livedata-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9bfda81c72d91dd55ac3f5c969251\transformed\lifecycle-livedata-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc9708d53e8f2793bb83dda03f7f198b\transformed\lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc9708d53e8f2793bb83dda03f7f198b\transformed\lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"
      provided="true"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35723f3ec1f7f19a19df180f25c2e72c\transformed\play-services-tasks-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35723f3ec1f7f19a19df180f25c2e72c\transformed\play-services-tasks-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f347490c75f494380ff69c9f50d27870\transformed\play-services-basement-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f347490c75f494380ff69c9f50d27870\transformed\play-services-basement-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a331cd04e5ebd3bc8db822c71d563d3a\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a331cd04e5ebd3bc8db822c71d563d3a\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80717a733d614bb44cb510a2438dabac\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80717a733d614bb44cb510a2438dabac\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"
      provided="true"/>
  <library
      name="com.github.PhilJay:MPAndroidChart:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c71d6b2c8bf4daf1d844d82d0aad70\transformed\MPAndroidChart-3.1.0\jars\classes.jar"
      resolved="com.github.PhilJay:MPAndroidChart:3.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c71d6b2c8bf4daf1d844d82d0aad70\transformed\MPAndroidChart-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\864d84c50db9a0ce49cc67bf35ecdd3a\transformed\runner-1.6.1\jars\classes.jar"
      resolved="androidx.test:runner:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\864d84c50db9a0ce49cc67bf35ecdd3a\transformed\runner-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40e39f7d00e1e6d8924509119de9b361\transformed\storage-1.5.0\jars\classes.jar"
      resolved="androidx.test.services:storage:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40e39f7d00e1e6d8924509119de9b361\transformed\storage-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\825cf4304ee9123a70328b8cf3c50269\transformed\monitor-1.7.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\825cf4304ee9123a70328b8cf3c50269\transformed\monitor-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd11c8c0fb69258ad9d0e9ecb212d4c3\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd11c8c0fb69258ad9d0e9ecb212d4c3\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e0d5a43066a07cdf2c570e81caae6d1\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e0d5a43066a07cdf2c570e81caae6d1\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac941a4f9bf2b621bd49b16708814fce\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac941a4f9bf2b621bd49b16708814fce\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.11.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098d10f6ec73f19d9d936d0d2332d328\transformed\exifinterface-1.0.0\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098d10f6ec73f19d9d936d0d2332d328\transformed\exifinterface-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ce7d0903b72d5587c01aaba5a30065b\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ce7d0903b72d5587c01aaba5a30065b\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:17.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4406db9a2bd650bcfc188fd527e46db2\transformed\firebase-components-17.1.5\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:17.1.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4406db9a2bd650bcfc188fd527e46db2\transformed\firebase-components-17.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"
      provided="true"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b210bddd55444e7da951a1e67a419d27\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b210bddd55444e7da951a1e67a419d27\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e66153af1fc7957406d639b1ef7f6ee\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e66153af1fc7957406d639b1ef7f6ee\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75e67f47b574ded484ed55a4033468cb\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75e67f47b574ded484ed55a4033468cb\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c993e987cfaf65ee58de96a2342808\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c993e987cfaf65ee58de96a2342808\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1950c847e3cf941e297593cba6b0a\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1950c847e3cf941e297593cba6b0a\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c14b1137c38ae1f74a23cc33e0e2275\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c14b1137c38ae1f74a23cc33e0e2275\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.0\f320478990d05e0cfaadd74f9619fd6027adbf37\kotlin-stdlib-jdk7-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.24\9928532f12c66ad816a625b3f9984f8368ca6d2b\kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ef4e809395bc4486a9b6fe6fa7b1dba\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ef4e809395bc4486a9b6fe6fa7b1dba\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-stub:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.52.1\16726293bd4430fab8deb37b1ea2ddf8e69b85de\grpc-stub-1.52.1.jar"
      resolved="io.grpc:grpc-stub:1.52.1"
      provided="true"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"
      provided="true"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd1a501fd9c1e49c5a01415a8e06937\transformed\espresso-idling-resource-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd1a501fd9c1e49c5a01415a8e06937\transformed\espresso-idling-resource-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.52.1\e50372c825b3b60a9c1c83895a8ac2209578260b\grpc-protobuf-lite-1.52.1.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.52.1"
      provided="true"/>
  <library
      name="io.grpc:grpc-android:1.52.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82ae2526d00cde287a5a3083bc3f6343\transformed\grpc-android-1.52.1\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.52.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82ae2526d00cde287a5a3083bc3f6343\transformed\grpc-android-1.52.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.52.1\aeef45c692943d6e5b5d6267dc826f3cec770437\grpc-okhttp-1.52.1.jar"
      resolved="io.grpc:grpc-okhttp:1.52.1"
      provided="true"/>
  <library
      name="io.grpc:grpc-core:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-core\1.52.1\c80b4701b0bef206b7208f4d3d941b21b527a311\grpc-core-1.52.1.jar"
      resolved="io.grpc:grpc-core:1.52.1"
      provided="true"/>
  <library
      name="io.grpc:grpc-api:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-api\1.52.1\250130924e11e3a0dfb5f508130940a1729d497c\grpc-api-1.52.1.jar"
      resolved="io.grpc:grpc-api:1.52.1"
      provided="true"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"
      provided="true"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.11.0\ed93d2e20549ad85f692d964788ec77520d78a8b\disklrucache-4.11.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.11.0"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:annotations:4.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.11.0\c57bae5a18147f8ae22f4da49baac875c6b6f84f\annotations-4.11.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.11.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:okhttp:3.14.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\3.14.9\3e6d101343c7ea687cd593e4990f73b25c878383\okhttp-3.14.9.jar"
      resolved="com.squareup.okhttp3:okhttp:3.14.9"
      provided="true"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\661fed9a09498c0c5f0b1dda196bab02\transformed\protolite-well-known-types-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\661fed9a09498c0c5f0b1dda196bab02\transformed\protolite-well-known-types-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio:1.17.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\1.17.2\78c7820b205002da4d2d137f6f312bd64b3d6049\okio-1.17.2.jar"
      resolved="com.squareup.okio:okio:1.17.2"
      provided="true"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.21.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.21.7\82b692be08383107fd1c6d44474b56df411edd27\protobuf-javalite-3.21.7.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.21.7"
      provided="true"/>
  <library
      name="io.grpc:grpc-context:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.52.1\4da95b74359b2dfdd5d85eee3b4682a3e720261c\grpc-context-1.52.1.jar"
      resolved="io.grpc:grpc-context:1.52.1"
      provided="true"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"
      provided="true"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"
      provided="true"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"
      provided="true"/>
</libraries>
