package com.example.onlinecoffeeshop.controller;

import android.app.Activity;
import android.content.Intent;
import android.widget.Toast;

import com.example.onlinecoffeeshop.MainActivity;
import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.view.auth.LoginActivity;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;

public class AuthController {
    private final FirebaseAuth auth;

    private final FirebaseFirestore firestore;
    public AuthController() {
        auth = FirebaseAuth.getInstance();
        firestore = FirebaseFirestore.getInstance();
    }

    public void signUp(){}

    public void signIn(Activity activity, String email, String password) {
        if(email.isEmpty() || password.isEmpty()) {
            Toast.makeText(activity, "Email và mật khẩu không được để trống.", Toast.LENGTH_SHORT).show();
            return;
        }
        auth.signInWithEmailAndPassword(email,password)
            .addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    // Sign in success
                    Toast.makeText(activity, "Sign in successfully", Toast.LENGTH_SHORT).show();
                    Intent intent = new Intent(activity, MainActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                } else {
                    // If sign in fails, display a message to the user.
                    Toast.makeText(activity, "Authentication failed: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                }
            });
    }

    public void signUp(Activity activity, String email, String password, User userData) {
        if (email.isEmpty() || password.isEmpty()) {
            Toast.makeText(activity, "Email và mật khẩu không được để trống.", Toast.LENGTH_SHORT).show();
            return;
        }

        auth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(activity, task -> {
                    if (task.isSuccessful()) {
                        String uid = task.getResult().getUser().getUid();
                        userData.setUid(uid);
                        userData.setEmail(email);

                        // Lưu user vào Firestore
                        firestore.collection("users")
                                .document(uid)
                                .set(userData)
                                .addOnSuccessListener(aVoid ->
                                        Toast.makeText(activity, "Đăng ký thành công!", Toast.LENGTH_SHORT).show()
                                )
                                .addOnFailureListener(e ->
                                        Toast.makeText(activity, "Lưu thông tin người dùng thất bại: " + e.getMessage(), Toast.LENGTH_LONG).show()
                                );
                        Intent intent = new Intent(activity, LoginActivity.class);
                        activity.startActivity(intent);
                        activity.finish();
                    } else {
                        Toast.makeText(activity, "Đăng ký thất bại: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
    }

    public void signOut(Activity activity) {
        auth.signOut();
        Toast.makeText(activity, "Đăng xuất thành công", Toast.LENGTH_SHORT).show();
        Intent intent = new Intent(activity, LoginActivity.class);
        activity.startActivity(intent);
        activity.finish();
    }

    public void resetPassword(Activity activity, String email) {
        if (email.isEmpty()) {
            Toast.makeText(activity, "Email không được để trống.", Toast.LENGTH_SHORT).show();
            return;
        }

        auth.sendPasswordResetEmail(email)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        Toast.makeText(activity, "Link đặt lại mật khẩu đã được gửi đến email của bạn", Toast.LENGTH_LONG).show();
                    } else {
                        Toast.makeText(activity, "Có lỗi xảy ra: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
    }

    public void getCurrentUser(Activity activity) {
        String uid = auth.getCurrentUser() != null ? auth.getCurrentUser().getUid() : null;
        if (uid == null) {
            Toast.makeText(activity, "You are not logged in", Toast.LENGTH_SHORT).show();
            Intent intent = new  Intent(activity, LoginActivity.class);
            activity.startActivity(intent);
            activity.finish();
            return;
        }

        firestore.collection("users").document(uid).get()
                .addOnSuccessListener(documentSnapshot -> {
                    if (documentSnapshot.exists()) {
                        User user = documentSnapshot.toObject(User.class);
                        Toast.makeText(activity, "User data retrieved successfully", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(activity, "User not found", Toast.LENGTH_SHORT).show();
                    }
                })
                .addOnFailureListener(e -> {
                    Toast.makeText(activity, "Error retrieving user data: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
    }

    public void changePassword(Activity activity, String oldPassword, String newPassword) {
        if (oldPassword.isEmpty() || newPassword.isEmpty()) {
            Toast.makeText(activity, "Mật khẩu cũ và mới không được để trống.", Toast.LENGTH_SHORT).show();
            return;
        }

        FirebaseUser user = auth.getCurrentUser();
        if (user == null) {
            Toast.makeText(activity, "Bạn chưa đăng nhập.", Toast.LENGTH_SHORT).show();
            return;
        }

        AuthCredential credential = EmailAuthProvider.getCredential(user.getEmail(), oldPassword);
        user.reauthenticate(credential)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        user.updatePassword(newPassword)
                                .addOnCompleteListener(updateTask -> {
                                    if (updateTask.isSuccessful()) {
                                        Toast.makeText(activity, "Đổi mật khẩu thành công", Toast.LENGTH_SHORT).show();
                                    } else {
                                        Toast.makeText(activity, "Đổi mật khẩu thất bại: " + updateTask.getException().getMessage(), Toast.LENGTH_LONG).show();
                                    }
                                });
                    } else {
                        Toast.makeText(activity, "Xác thực không thành công: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
    }
}
