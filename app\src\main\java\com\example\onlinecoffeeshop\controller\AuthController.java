package com.example.onlinecoffeeshop.controller;

import android.content.Context;
import android.util.Log;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.FirebaseFirestore;

import java.util.HashMap;
import java.util.Map;

public class AuthController {
    private static final String TAG = "AuthController";

    private FirebaseAuth firebaseAuth;
    private FirebaseFirestore firestore;
    private Context context;

    public AuthController(Context context) {
        this.context = context;
        this.firebaseAuth = FirebaseAuth.getInstance();
        this.firestore = FirebaseFirestore.getInstance();
    }

    // Interface for callbacks
    public interface AuthCallback {
        void onSuccess();
        void onFailure(String error);
    }

    public interface UserCallback {
        void onSuccess(Map<String, Object> userData);
        void onFailure(String error);
    }

    // Register user
    public void registerUser(String email, String password, String fullname, String dob,
                           String address, String phone, AuthCallback callback) {
        firebaseAuth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(authTask -> {
                    if (authTask.isSuccessful()) {
                        FirebaseUser firebaseUser = authTask.getResult().getUser();
                        if (firebaseUser != null) {
                            // Save user data to Firestore
                            saveUserToFirestore(firebaseUser.getUid(), fullname, email, dob, address, phone, callback);
                        } else {
                            callback.onFailure("Đăng ký thất bại");
                        }
                    } else {
                        String errorMessage = getErrorMessage(authTask.getException());
                        callback.onFailure(errorMessage);
                        Log.e(TAG, "Registration failed", authTask.getException());
                    }
                });
    }

    // Login user
    public void loginUser(String email, String password, AuthCallback callback) {
        firebaseAuth.signInWithEmailAndPassword(email, password)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = firebaseAuth.getCurrentUser();
                        Log.d(TAG, "Login successful: " + user.getEmail());
                        callback.onSuccess();
                    } else {
                        String errorMessage = getErrorMessage(task.getException());
                        Log.e(TAG, "Login failed", task.getException());
                        callback.onFailure(errorMessage);
                    }
                });
    }

    // Get current user data
    public void getCurrentUser(UserCallback callback) {
        FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
        if (firebaseUser != null) {
            firestore.collection("users")
                    .document(firebaseUser.getUid())
                    .get()
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful() && task.getResult() != null) {
                            Map<String, Object> userData = task.getResult().getData();
                            if (userData != null) {
                                callback.onSuccess(userData);
                            } else {
                                callback.onFailure("Không thể tải thông tin người dùng");
                            }
                        } else {
                            Log.e(TAG, "Failed to load user profile", task.getException());
                            callback.onFailure("Không thể tải thông tin người dùng");
                        }
                    });
        } else {
            callback.onFailure("Người dùng chưa đăng nhập");
        }
    }

    // Update user profile
    public void updateUserProfile(String fullname, String dob, String address, String phone, AuthCallback callback) {
        FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
        if (firebaseUser != null) {
            Map<String, Object> updates = new HashMap<>();
            updates.put("fullname", fullname);
            updates.put("dob", dob);
            updates.put("address", address);
            updates.put("phone", phone);

            firestore.collection("users")
                    .document(firebaseUser.getUid())
                    .update(updates)
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful()) {
                            callback.onSuccess();
                        } else {
                            Log.e(TAG, "Failed to update profile", task.getException());
                            callback.onFailure("Cập nhật thông tin thất bại");
                        }
                    });
        } else {
            callback.onFailure("Người dùng chưa đăng nhập");
        }
    }

    // Update password
    public void updatePassword(String newPassword, AuthCallback callback) {
        FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
        if (firebaseUser != null) {
            firebaseUser.updatePassword(newPassword)
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful()) {
                            callback.onSuccess();
                        } else {
                            Log.e(TAG, "Failed to change password", task.getException());
                            callback.onFailure("Đổi mật khẩu thất bại. Vui lòng thử lại");
                        }
                    });
        } else {
            callback.onFailure("Người dùng chưa đăng nhập");
        }
    }

    // Reset password
    public void resetPassword(String email, AuthCallback callback) {
        firebaseAuth.sendPasswordResetEmail(email)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        callback.onSuccess();
                    } else {
                        callback.onFailure("Có lỗi xảy ra. Vui lòng thử lại");
                    }
                });
    }

    // Check if user is logged in
    public boolean isUserLoggedIn() {
        return firebaseAuth.getCurrentUser() != null;
    }

    // Logout user
    public void logoutUser() {
        firebaseAuth.signOut();
    }

    // Save user to Firestore
    private void saveUserToFirestore(String uid, String fullname, String email, String dob,
                                   String address, String phone, AuthCallback callback) {
        Map<String, Object> user = new HashMap<>();
        user.put("uid", uid);
        user.put("fullname", fullname);
        user.put("email", email);
        user.put("dob", dob);
        user.put("address", address);
        user.put("phone", phone);
        user.put("role", "user");
        user.put("avatar", null);

        firestore.collection("users")
                .document(uid)
                .set(user)
                .addOnCompleteListener(firestoreTask -> {
                    if (firestoreTask.isSuccessful()) {
                        Log.d(TAG, "User data saved to Firestore successfully");
                        callback.onSuccess();
                    } else {
                        Log.e(TAG, "Failed to save user data to Firestore", firestoreTask.getException());
                        // Delete the Firebase Auth account if Firestore save fails
                        FirebaseUser currentUser = firebaseAuth.getCurrentUser();
                        if (currentUser != null) {
                            currentUser.delete();
                        }
                        callback.onFailure("Đăng ký thất bại. Vui lòng thử lại.");
                    }
                });
    }

    // Get error message from exception
    private String getErrorMessage(Exception exception) {
        if (exception == null) return "Có lỗi xảy ra";

        String message = exception.getMessage();
        if (message == null) return "Có lỗi xảy ra";

        // Convert Firebase error messages to Vietnamese
        if (message.contains("email address is already in use")) {
            return "Email này đã được sử dụng";
        } else if (message.contains("password is invalid")) {
            return "Mật khẩu không đúng";
        } else if (message.contains("no user record")) {
            return "Email không tồn tại";
        } else if (message.contains("weak-password")) {
            return "Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn";
        } else if (message.contains("invalid-email")) {
            return "Email không hợp lệ";
        } else if (message.contains("network error")) {
            return "Lỗi kết nối mạng. Vui lòng thử lại";
        }

        return "Có lỗi xảy ra: " + message;
    }