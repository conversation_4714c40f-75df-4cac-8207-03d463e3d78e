package com.example.onlinecoffeeshop.controller;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.example.onlinecoffeeshop.MainActivity;
import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.view.auth.LoginActivity;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.FirebaseFirestore;

public class AuthController {
    private static final String TAG = "AuthController";
    private FirebaseAuth auth;
    private FirebaseFirestore firestore;

    public AuthController() {
        auth = FirebaseAuth.getInstance();
        firestore = FirebaseFirestore.getInstance();
        Log.d(TAG, "AuthController initialized");
    }

    // Register function
    public void signUp(Activity activity, String email, String password, User user) {
        Log.d(TAG, "Starting registration for email: " + email);

        auth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(activity, task -> {
                    Log.d(TAG, "Registration task completed. Success: " + task.isSuccessful());

                    if (task.isSuccessful()) {
                        FirebaseUser firebaseUser = auth.getCurrentUser();
                        if (firebaseUser != null) {
                            Log.d(TAG, "Firebase user created with UID: " + firebaseUser.getUid());
                            user.setUid(firebaseUser.getUid());
                            user.setEmail(email);

                            // Save user to Firestore
                            firestore.collection("users").document(firebaseUser.getUid())
                                    .set(user)
                                    .addOnSuccessListener(aVoid -> {
                                        Log.d(TAG, "User data saved to Firestore successfully");
                                        Toast.makeText(activity, "Đăng ký thành công!", Toast.LENGTH_SHORT).show();
                                        navigateToLogin(activity);
                                    })
                                    .addOnFailureListener(e -> {
                                        Log.e(TAG, "Failed to save user data to Firestore", e);
                                        Toast.makeText(activity, "Lỗi lưu thông tin: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                                    });
                        } else {
                            Log.e(TAG, "Firebase user is null after successful registration");
                            Toast.makeText(activity, "Lỗi: Không thể lấy thông tin người dùng", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Log.e(TAG, "Registration failed", task.getException());
                        String errorMessage = task.getException() != null ? task.getException().getMessage() : "Unknown error";
                        Toast.makeText(activity, "Đăng ký thất bại: " + errorMessage, Toast.LENGTH_LONG).show();
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Registration failed with exception", e);
                    Toast.makeText(activity, "Lỗi kết nối: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
    }

    // Login function
    public void signIn(Activity activity, String email, String password) {
        Log.d(TAG, "Starting login for email: " + email);

        // Add immediate feedback
        Toast.makeText(activity, "Đang đăng nhập...", Toast.LENGTH_SHORT).show();

        // Add timeout handler
        Handler timeoutHandler = new Handler(Looper.getMainLooper());
        boolean[] callbackReceived = {false};

        Runnable timeoutRunnable = () -> {
            if (!callbackReceived[0]) {
                Log.e(TAG, "Login timeout - no callback received after 10 seconds");
                Toast.makeText(activity, "Timeout: Vui lòng kiểm tra kết nối mạng", Toast.LENGTH_LONG).show();
            }
        };

        timeoutHandler.postDelayed(timeoutRunnable, 10000); // 10 second timeout

        auth.signInWithEmailAndPassword(email, password)
                .addOnCompleteListener(task -> {
                    callbackReceived[0] = true;
                    timeoutHandler.removeCallbacks(timeoutRunnable);

                    Log.d(TAG, "Login task completed. Success: " + task.isSuccessful());

                    activity.runOnUiThread(() -> {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "Login successful");
                            Toast.makeText(activity, "Đăng nhập thành công!", Toast.LENGTH_SHORT).show();
                            navigateToMain(activity);
                        } else {
                            Log.e(TAG, "Login failed", task.getException());
                            String errorMessage = task.getException() != null ? task.getException().getMessage() : "Unknown error";
                            Toast.makeText(activity, "Đăng nhập thất bại: " + errorMessage, Toast.LENGTH_LONG).show();
                        }
                    });
                })
                .addOnFailureListener(e -> {
                    callbackReceived[0] = true;
                    timeoutHandler.removeCallbacks(timeoutRunnable);

                    Log.e(TAG, "Login failed with exception", e);
                    activity.runOnUiThread(() -> {
                        Toast.makeText(activity, "Lỗi kết nối: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                });
    }

    // Reset password function
    public void resetPassword(Activity activity, String email) {
        auth.sendPasswordResetEmail(email)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        Toast.makeText(activity, "Link đặt lại mật khẩu đã được gửi đến email của bạn", Toast.LENGTH_LONG).show();
                    } else {
                        Toast.makeText(activity, "Có lỗi xảy ra: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
    }

    public boolean isUserLoggedIn() {
        return auth.getCurrentUser() != null;
    }

    public void signOut(Activity activity) {
        auth.signOut();
        Toast.makeText(activity, "Đăng xuất thành công", Toast.LENGTH_SHORT).show();
        navigateToLogin(activity);
    }

    private void navigateToLogin(Activity activity) {
        Intent intent = new Intent(activity, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        activity.startActivity(intent);
        activity.finish();
    }

    private void navigateToMain(Activity activity) {
        Intent intent = new Intent(activity, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        activity.startActivity(intent);
        activity.finish();
    }
}
