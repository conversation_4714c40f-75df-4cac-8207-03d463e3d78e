package com.example.onlinecoffeeshop.controller;

import android.content.Context;
import android.util.Log;

import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.repository.UserRepository;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.Tasks;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.DocumentSnapshot;

public class AuthController {
    private static final String TAG = "AuthController";
    private final FirebaseAuth mAuth;
    private final UserRepository userRepository;
    private final Context context;

    public AuthController(Context context) {
        this.context = context;
        this.mAuth = FirebaseAuth.getInstance();
        this.userRepository = new UserRepository();
    }

    // Register new user
    public Task<User> registerUser(String email, String password, String fullname,
                                  String dob, String address, String phone) {
        return mAuth.createUserWithEmailAndPassword(email, password)
                .continueWithTask(task -> {
                    if (!task.isSuccessful()) {
                        throw task.getException();
                    }

                    AuthResult authResult = task.getResult();
                    FirebaseUser firebaseUser = authResult.getUser();

                    if (firebaseUser == null) {
                        throw new Exception("Failed to create user");
                    }

                    // Create User object with default role 'user'
                    User user = new User(firebaseUser.getUid(), fullname, dob, null,
                                       address, phone, "user", email);

                    // Save user to Firestore
                    return userRepository.saveUser(user)
                            .continueWith(saveTask -> {
                                if (!saveTask.isSuccessful()) {
                                    // If Firestore save fails, delete the Firebase Auth user
                                    firebaseUser.delete();
                                    throw saveTask.getException();
                                }
                                return user;
                            });
                });
    }

    // Login user
    public Task<User> loginUser(String email, String password) {
        return mAuth.signInWithEmailAndPassword(email, password)
                .continueWithTask(task -> {
                    if (!task.isSuccessful()) {
                        throw task.getException();
                    }

                    FirebaseUser firebaseUser = mAuth.getCurrentUser();
                    if (firebaseUser == null) {
                        throw new Exception("Login failed");
                    }

                    // Get user data from Firestore
                    return userRepository.getUserByUid(firebaseUser.getUid())
                            .continueWith(userTask -> {
                                if (!userTask.isSuccessful()) {
                                    throw userTask.getException();
                                }

                                DocumentSnapshot document = userTask.getResult();
                                if (!document.exists()) {
                                    throw new Exception("User data not found");
                                }

                                return document.toObject(User.class);
                            });
                });
    }

    // Logout user
    public void logoutUser() {
        mAuth.signOut();
        Log.d(TAG, "User logged out");
    }

    // Get current user
    public Task<User> getCurrentUser() {
        FirebaseUser firebaseUser = mAuth.getCurrentUser();
        if (firebaseUser == null) {
            return Tasks.forException(new Exception("No user logged in"));
        }

        return userRepository.getUserByUid(firebaseUser.getUid())
                .continueWith(task -> {
                    if (!task.isSuccessful()) {
                        throw task.getException();
                    }

                    DocumentSnapshot document = task.getResult();
                    if (!document.exists()) {
                        throw new Exception("User data not found");
                    }

                    return document.toObject(User.class);
                });
    }

    // Update user profile
    public Task<Void> updateUserProfile(String fullname, String dob, String avatar,
                                       String address, String phone) {
        FirebaseUser firebaseUser = mAuth.getCurrentUser();
        if (firebaseUser == null) {
            return Tasks.forException(new Exception("No user logged in"));
        }

        return userRepository.updateUserProfile(firebaseUser.getUid(), fullname,
                                              dob, avatar, address, phone);
    }

    // Check if user is logged in
    public boolean isUserLoggedIn() {
        return mAuth.getCurrentUser() != null;
    }

    // Get Firebase User
    public FirebaseUser getFirebaseUser() {
        return mAuth.getCurrentUser();
    }

    // Reset password
    public Task<Void> resetPassword(String email) {
        return mAuth.sendPasswordResetEmail(email);
    }

    // Update email
    public Task<Void> updateEmail(String newEmail) {
        FirebaseUser firebaseUser = mAuth.getCurrentUser();
        if (firebaseUser == null) {
            return Tasks.forException(new Exception("No user logged in"));
        }

        return firebaseUser.updateEmail(newEmail)
                .continueWithTask(task -> {
                    if (!task.isSuccessful()) {
                        throw task.getException();
                    }

                    // Update email in Firestore
                    return userRepository.getUserByUid(firebaseUser.getUid())
                            .continueWithTask(userTask -> {
                                if (!userTask.isSuccessful()) {
                                    throw userTask.getException();
                                }

                                DocumentSnapshot document = userTask.getResult();
                                if (!document.exists()) {
                                    throw new Exception("User data not found");
                                }

                                User user = document.toObject(User.class);
                                user.setEmail(newEmail);
                                return userRepository.saveUser(user);
                            });
                });
    }

    // Update password
    public Task<Void> updatePassword(String newPassword) {
        FirebaseUser firebaseUser = mAuth.getCurrentUser();
        if (firebaseUser == null) {
            return Tasks.forException(new Exception("No user logged in"));
        }

        return firebaseUser.updatePassword(newPassword);
    }
}
