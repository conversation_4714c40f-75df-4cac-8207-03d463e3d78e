package com.example.onlinecoffeeshop.controller;

import android.content.Context;
import android.util.Log;

import com.example.onlinecoffeeshop.helper.FirebaseAuthHelper;
import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.repository.UserRepository;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseUser;

/**
 * AuthController handles all authentication business logic
 * Coordinates between FirebaseAuthHelper and UserRepository
 */
public class AuthController {
    private static final String TAG = "AuthController";
    private static FirebaseAuth

    private final Context context;

    public AuthController(Context context) {
        this.context = context;
        this.authHelper = new FirebaseAuthHelper();
        this.userRepository = new UserRepository();
    }

    /**
     * Register new user with email and password
     * Creates Firebase Auth account and saves user data to Firestore
     */
    public void registerUser(String email, String password, String fullname,
                                 String dob, String address, String phone) {
        Log.d(TAG, "Starting user registration for email: " + email);

        TaskCompletionSource<User> taskCompletionSource = new TaskCompletionSource<>();

        // Step 1: Create Firebase Auth account
        authHelper.registerWithEmailAndPassword(email, password)
                .addOnCompleteListener(authTask -> {
                    if (authTask.isSuccessful() && authTask.getResult() != null) {
                        FirebaseUser firebaseUser = authTask.getResult().getUser();

                        if (firebaseUser != null) {
                            Log.d(TAG, "Firebase Auth registration successful for UID: " + firebaseUser.getUid());

                            // Step 2: Create User object and save to Firestore
                            User user = new User(
                                firebaseUser.getUid(),
                                fullname,
                                dob,
                                null, // avatar will be null initially
                                address,
                                phone,
                                "user", // default role
                                email
                            );

                            // Step 3: Save user to Firestore
                            Log.d(TAG, "Attempting to save user to Firestore with UID: " + user.getUid());
                            Log.d(TAG, "User data: " + user.getFullname() + ", " + user.getEmail());

                            userRepository.saveUser(user)
                                    .addOnCompleteListener(firestoreTask -> {
                                        if (firestoreTask.isSuccessful()) {
                                            Log.d(TAG, "User data saved to Firestore successfully");
                                            taskCompletionSource.setResult(user);
                                        } else {
                                            Exception exception = firestoreTask.getException();
                                            Log.e(TAG, "Failed to save user data to Firestore", exception);

                                            // Enhanced error logging
                                            if (exception != null) {
                                                Log.e(TAG, "Exception type: " + exception.getClass().getSimpleName());
                                                Log.e(TAG, "Exception message: " + exception.getMessage());
                                                if (exception.getCause() != null) {
                                                    Log.e(TAG, "Exception cause: " + exception.getCause().getMessage());
                                                }
                                            }

                                            // If Firestore save fails, we should delete the Firebase Auth account
                                            deleteFirebaseAuthAccount(firebaseUser);
                                            taskCompletionSource.setException(
                                                exception != null ? exception : new Exception("Failed to save user data")
                                            );
                                        }
                                    });
                        } else {
                            Log.e(TAG, "Firebase user is null after successful registration");
                            taskCompletionSource.setException(new Exception("Registration failed: User is null"));
                        }
                    } else {
                        Log.e(TAG, "Firebase Auth registration failed", authTask.getException());
                        taskCompletionSource.setException(
                            authTask.getException() != null ?
                            authTask.getException() :
                            new Exception("Registration failed")
                        );
                    }
                });

        return taskCompletionSource.getTask();
    }

    /**
     * Login user with email and password
     * Authenticates with Firebase and retrieves user data from Firestore
     */
    public void loginUser(String email, String password) {
        Log.d(TAG, "Starting user login for email: " + email);

        TaskCompletionSource<User> taskCompletionSource = new TaskCompletionSource<>();

        // Step 1: Authenticate with Firebase Auth
        authHelper.signInWithEmailAndPassword(email, password)
                .addOnCompleteListener(authTask -> {
                    if (authTask.isSuccessful() && authTask.getResult() != null) {
                        FirebaseUser firebaseUser = authTask.getResult().getUser();

                        if (firebaseUser != null) {
                            Log.d(TAG, "Firebase Auth login successful for UID: " + firebaseUser.getUid());

                            // Step 2: Get user data from Firestore
                            userRepository.getUserByUid(firebaseUser.getUid())
                                    .addOnCompleteListener(firestoreTask -> {
                                        if (firestoreTask.isSuccessful() && firestoreTask.getResult() != null) {
                                            DocumentSnapshot document = firestoreTask.getResult();

                                            if (document.exists()) {
                                                User user = document.toObject(User.class);
                                                if (user != null) {
                                                    Log.d(TAG, "User data retrieved from Firestore successfully");
                                                    taskCompletionSource.setResult(user);
                                                } else {
                                                    Log.e(TAG, "Failed to convert document to User object");
                                                    taskCompletionSource.setException(new Exception("Failed to retrieve user data"));
                                                }
                                            } else {
                                                Log.e(TAG, "User document does not exist in Firestore");
                                                taskCompletionSource.setException(new Exception("User data not found"));
                                            }
                                        } else {
                                            Log.e(TAG, "Failed to retrieve user data from Firestore", firestoreTask.getException());
                                            taskCompletionSource.setException(
                                                firestoreTask.getException() != null ?
                                                firestoreTask.getException() :
                                                new Exception("Failed to retrieve user data")
                                            );
                                        }
                                    });
                        } else {
                            Log.e(TAG, "Firebase user is null after successful login");
                            taskCompletionSource.setException(new Exception("Login failed: User is null"));
                        }
                    } else {
                        Log.e(TAG, "Firebase Auth login failed", authTask.getException());
                        taskCompletionSource.setException(
                            authTask.getException() != null ?
                            authTask.getException() :
                            new Exception("Login failed")
                        );
                    }
                });

        return taskCompletionSource.getTask();
    }

    /**
     * Send password reset email
     */
    public void sendPasswordResetEmail(String email) {
        Log.d(TAG, "Sending password reset email to: " + email);
        return authHelper.sendPasswordResetEmail(email);
    }

    /**
     * Sign out current user
     */
    public void signOut() {
        Log.d(TAG, "Signing out current user");
        authHelper.signOut();
    }

    /**
     * Check if user is logged in
     */
    public boolean isUserLoggedIn() {
        return authHelper.isUserLoggedIn();
    }

    /**
     * Get current user data from Firestore
     */
    public void getCurrentUser() {
        String currentUserUid = authHelper.getCurrentUserUid();

        if (currentUserUid != null) {
            TaskCompletionSource<User> taskCompletionSource = new TaskCompletionSource<>();

            userRepository.getUserByUid(currentUserUid)
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful() && task.getResult() != null) {
                            DocumentSnapshot document = task.getResult();

                            if (document.exists()) {
                                User user = document.toObject(User.class);
                                if (user != null) {
                                    taskCompletionSource.setResult(user);
                                } else {
                                    taskCompletionSource.setException(new Exception("Failed to convert document to User object"));
                                }
                            } else {
                                taskCompletionSource.setException(new Exception("User document does not exist"));
                            }
                        } else {
                            taskCompletionSource.setException(
                                task.getException() != null ?
                                task.getException() :
                                new Exception("Failed to retrieve user data")
                            );
                        }
                    });

            return taskCompletionSource.getTask();
        } else {
            TaskCompletionSource<User> taskCompletionSource = new TaskCompletionSource<>();
            taskCompletionSource.setException(new Exception("No user is currently logged in"));
            return taskCompletionSource.getTask();
        }
    }

    /**
     * Update user profile
     */
    public void updateUserProfile(String fullname, String dob, String avatar, String address, String phone) {
        String currentUserUid = authHelper.getCurrentUserUid();

        if (currentUserUid != null) {
            Log.d(TAG, "Updating user profile for UID: " + currentUserUid);
            return userRepository.updateUserProfile(currentUserUid, fullname, dob, avatar, address, phone);
        } else {
            TaskCompletionSource<Void> taskCompletionSource = new TaskCompletionSource<>();
            taskCompletionSource.setException(new Exception("No user is currently logged in"));
            return taskCompletionSource.getTask();
        }
    }

    /**
     * Change user password
     */
    public void changePassword(String currentPassword, String newPassword) {
        Log.d(TAG, "Changing user password");

        TaskCompletionSource<Void> taskCompletionSource = new TaskCompletionSource<>();

        // Step 1: Re-authenticate user with current password
        authHelper.reauthenticateUser(currentPassword)
                .addOnCompleteListener(reauthTask -> {
                    if (reauthTask.isSuccessful()) {
                        Log.d(TAG, "User re-authentication successful");

                        // Step 2: Update password
                        authHelper.updatePassword(newPassword)
                                .addOnCompleteListener(updateTask -> {
                                    if (updateTask.isSuccessful()) {
                                        Log.d(TAG, "Password updated successfully");
                                        taskCompletionSource.setResult(null);
                                    } else {
                                        Log.e(TAG, "Failed to update password", updateTask.getException());
                                        taskCompletionSource.setException(
                                            updateTask.getException() != null ?
                                            updateTask.getException() :
                                            new Exception("Failed to update password")
                                        );
                                    }
                                });
                    } else {
                        Log.e(TAG, "User re-authentication failed", reauthTask.getException());
                        taskCompletionSource.setException(
                            reauthTask.getException() != null ?
                            reauthTask.getException() :
                            new Exception("Current password is incorrect")
                        );
                    }
                });

        return taskCompletionSource.getTask();
    }

    /**
     * Delete Firebase Auth account (used when Firestore save fails during registration)
     */
    private void deleteFirebaseAuthAccount(FirebaseUser firebaseUser) {
        if (firebaseUser != null) {
            Log.w(TAG, "Deleting Firebase Auth account due to Firestore save failure");
            firebaseUser.delete()
                    .addOnCompleteListener(deleteTask -> {
                        if (deleteTask.isSuccessful()) {
                            Log.d(TAG, "Firebase Auth account deleted successfully");
                        } else {
                            Log.e(TAG, "Failed to delete Firebase Auth account", deleteTask.getException());
                        }
                    });
        }
    }

    /**
     * Get current user UID
     */
    public String getCurrentUserUid() {
        return authHelper.getCurrentUserUid();
    }

    /**
     * Get current user email
     */
    public String getCurrentUserEmail() {
        return authHelper.getCurrentUserEmail();
    }
}
