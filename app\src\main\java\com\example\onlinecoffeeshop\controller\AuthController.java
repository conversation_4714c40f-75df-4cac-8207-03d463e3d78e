package com.example.onlinecoffeeshop.controller;

import android.app.Activity;
import android.content.Intent;
import android.widget.Toast;

import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.view.auth.LoginActivity;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;

public class AuthController {
    private final FirebaseAuth auth = FirebaseAuth.getInstance();

    private final FirebaseFirestore db = FirebaseFirestore.getInstance();

    public void register(Activity activity, String email, String password, String fullname, String dob, String avatar, String address, String phone, String role) {
        // Implement registration logic using FirebaseAuth
        auth.createUserWithEmailAndPassword(email,password)
            .addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    // User registration successful, now save user details to Firestore
                    String uid = task.getResult().getUser().getUid();
                    User newUser = new User(uid, fullname, dob, avatar, address, phone, role, email);
                    db.collection("users").document(uid).set(newUser)
                        .addOnSuccessListener(aVoid -> {

                            Intent intent = new Intent(activity, LoginActivity.class);
                            Toast.makeText(activity, "Đăng ký thành công! Chuyển hươg đén trang đăng nhập", Toast.LENGTH_SHORT).show();
                            activity.startActivity(intent);
                            activity.finish();
                            // User details saved successfully

                        })
                        .addOnFailureListener(e -> {
                            // Handle failure to save user details
                            Toast.makeText(activity, "Lỗi khi lưu thông tin nguời dùng:" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        });
                } else {
                    // Handle registration failure
                    Toast.makeText(activity, "Đăng ký thất bại: " + task.getException().getMessage(), Toast.LENGTH_SHORT).show();
                }
            });
        // Validate inputs and create a new user in Firestore
    }
}
