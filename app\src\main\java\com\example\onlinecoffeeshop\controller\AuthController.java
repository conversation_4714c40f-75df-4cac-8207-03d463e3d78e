package com.example.onlinecoffeeshop.controller;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

import com.example.onlinecoffeeshop.MainActivity;
import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.view.auth.LoginActivity;
import com.example.onlinecoffeeshop.view.home.HomeActivity;
import com.google.firebase.auth.AuthCredential;
import com.google.firebase.auth.EmailAuthProvider;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.FirebaseFirestore;

import java.util.HashMap;
import java.util.Map;

public class AuthController {
    private static final String TAG = "AuthController";
    private final FirebaseAuth auth;
    private final FirebaseFirestore firestore;

    public AuthController() {
        auth = FirebaseAuth.getInstance();
        firestore = FirebaseFirestore.getInstance();
    }

    // Callback interfaces
    public interface AuthCallback {
        void onSuccess();
        void onFailure(String error);
    }

    public interface UserCallback {
        void onSuccess(Map<String, Object> userData);
        void onFailure(String error);
    }

    // Login user with callback
    public void loginUser(String email, String password, AuthCallback callback) {
        if (email.isEmpty() || password.isEmpty()) {
            callback.onFailure("Email và mật khẩu không được để trống");
            return;
        }

        Log.d(TAG, "Attempting to login user: " + email);
        auth.signInWithEmailAndPassword(email, password)
            .addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    Log.d(TAG, "Login successful for user: " + email);
                    callback.onSuccess();
                } else {
                    String error = task.getException() != null ?
                        task.getException().getMessage() : "Đăng nhập thất bại";
                    Log.e(TAG, "Login failed: " + error);
                    callback.onFailure(error);
                }
            });
    }

    // Register user with callback
    public void registerUser(String email, String password, User userData, AuthCallback callback) {
        if (email.isEmpty() || password.isEmpty()) {
            callback.onFailure("Email và mật khẩu không được để trống");
            return;
        }

        Log.d(TAG, "Attempting to register user: " + email);
        auth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser firebaseUser = task.getResult().getUser();
                        if (firebaseUser != null) {
                            String uid = firebaseUser.getUid();
                            userData.setUid(uid);
                            userData.setEmail(email);

                            Log.d(TAG, "Firebase Auth successful, saving user to Firestore: " + uid);

                            // Tạo Map để lưu vào Firestore với đúng cấu trúc
                            Map<String, Object> userMap = new HashMap<>();
                            userMap.put("uid", uid);
                            userMap.put("fullname", userData.getFullname());
                            userMap.put("dob", userData.getDob());
                            userMap.put("avatar", userData.getAvatar() != null ? userData.getAvatar() : "");
                            userMap.put("address", userData.getAddress());
                            userMap.put("phone", userData.getPhone());
                            userMap.put("role", userData.getRole());
                            userMap.put("email", email);

                            // Lưu user vào Firestore
                            firestore.collection("users")
                                    .document(uid)
                                    .set(userMap)
                                    .addOnSuccessListener(aVoid -> {
                                        Log.d(TAG, "User data saved to Firestore successfully");
                                        callback.onSuccess();
                                    })
                                    .addOnFailureListener(e -> {
                                        Log.e(TAG, "Failed to save user data to Firestore: " + e.getMessage());
                                        callback.onFailure("Lưu thông tin người dùng thất bại: " + e.getMessage());
                                    });
                        } else {
                            Log.e(TAG, "FirebaseUser is null after successful registration");
                            callback.onFailure("Lỗi hệ thống: Không thể lấy thông tin người dùng");
                        }
                    } else {
                        String error = task.getException() != null ?
                            task.getException().getMessage() : "Đăng ký thất bại";
                        Log.e(TAG, "Firebase Auth registration failed: " + error);
                        callback.onFailure(error);
                    }
                });
    }

    public void signOut(Activity activity) {
        auth.signOut();
        Toast.makeText(activity, "Đăng xuất thành công", Toast.LENGTH_SHORT).show();
        Intent intent = new Intent(activity, LoginActivity.class);
        activity.startActivity(intent);
        activity.finish();
    }

    // Reset password with callback
    public void resetPassword(String email, AuthCallback callback) {
        if (email.isEmpty()) {
            callback.onFailure("Email không được để trống");
            return;
        }

        Log.d(TAG, "Attempting to reset password for: " + email);
        auth.sendPasswordResetEmail(email)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        Log.d(TAG, "Password reset email sent successfully");
                        callback.onSuccess();
                    } else {
                        String error = task.getException() != null ?
                            task.getException().getMessage() : "Có lỗi xảy ra";
                        Log.e(TAG, "Password reset failed: " + error);
                        callback.onFailure(error);
                    }
                });
    }

    // Get current user data with callback
    public void getCurrentUser(UserCallback callback) {
        FirebaseUser firebaseUser = auth.getCurrentUser();
        if (firebaseUser == null) {
            Log.d(TAG, "No user is currently logged in");
            callback.onFailure("Bạn chưa đăng nhập");
            return;
        }

        String uid = firebaseUser.getUid();
        Log.d(TAG, "Getting user data for UID: " + uid);

        firestore.collection("users").document(uid).get()
                .addOnSuccessListener(documentSnapshot -> {
                    if (documentSnapshot.exists()) {
                        Map<String, Object> userData = documentSnapshot.getData();
                        Log.d(TAG, "User data retrieved successfully: " + userData);
                        callback.onSuccess(userData);
                    } else {
                        Log.w(TAG, "User document does not exist in Firestore");
                        callback.onFailure("Không tìm thấy thông tin người dùng");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error retrieving user data: " + e.getMessage());
                    callback.onFailure("Lỗi khi lấy thông tin người dùng: " + e.getMessage());
                });
    }

    // Check if user is logged in
    public boolean isUserLoggedIn() {
        return auth.getCurrentUser() != null;
    }

    public void changePassword(Activity activity, String oldPassword, String newPassword) {
        if (oldPassword.isEmpty() || newPassword.isEmpty()) {
            Toast.makeText(activity, "Mật khẩu cũ và mới không được để trống.", Toast.LENGTH_SHORT).show();
            return;
        }

        FirebaseUser user = auth.getCurrentUser();
        if (user == null) {
            Toast.makeText(activity, "Bạn chưa đăng nhập.", Toast.LENGTH_SHORT).show();
            return;
        }

        AuthCredential credential = EmailAuthProvider.getCredential(user.getEmail(), oldPassword);
        user.reauthenticate(credential)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        user.updatePassword(newPassword)
                                .addOnCompleteListener(updateTask -> {
                                    if (updateTask.isSuccessful()) {
                                        Toast.makeText(activity, "Đổi mật khẩu thành công", Toast.LENGTH_SHORT).show();
                                    } else {
                                        Toast.makeText(activity, "Đổi mật khẩu thất bại: " + updateTask.getException().getMessage(), Toast.LENGTH_LONG).show();
                                    }
                                });
                    } else {
                        Toast.makeText(activity, "Xác thực không thành công: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
    }
}
