package com.example.onlinecoffeeshop.controller;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.example.onlinecoffeeshop.MainActivity;
import com.example.onlinecoffeeshop.model.User;
import com.example.onlinecoffeeshop.view.auth.LoginActivity;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.FirebaseFirestore;

public class AuthController {
    private static final String TAG = "AuthController";
    private FirebaseAuth auth;
    private FirebaseFirestore firestore;

    public AuthController() {
        Log.d(TAG, "Initializing AuthController...");
        auth = FirebaseAuth.getInstance();
        firestore = FirebaseFirestore.getInstance();
        Log.d(TAG, "AuthController initialized successfully");

        // Test Firebase connection
        testFirebaseConnection();
    }

    private void testFirebaseConnection() {
        Log.d(TAG, "Testing Firebase connection...");
        FirebaseUser currentUser = auth.getCurrentUser();
        Log.d(TAG, "Current user: " + (currentUser != null ? currentUser.getEmail() : "null"));
    }

    // Register function
    public void signUp(Activity activity, String email, String password, User user) {
        Log.d(TAG, "Starting registration for email: " + email);

        // Show immediate feedback
        Toast.makeText(activity, "Đang đăng ký...", Toast.LENGTH_SHORT).show();

        auth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(activity, task -> {
                    Log.d(TAG, "Registration task completed. Success: " + task.isSuccessful());

                    activity.runOnUiThread(() -> {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "Registration successful");
                            FirebaseUser firebaseUser = auth.getCurrentUser();
                            if (firebaseUser != null) {
                                Log.d(TAG, "Firebase user created with UID: " + firebaseUser.getUid());
                                user.setUid(firebaseUser.getUid());
                                user.setEmail(email);

                                // Save user to Firestore
                                firestore.collection("users").document(firebaseUser.getUid())
                                        .set(user)
                                        .addOnSuccessListener(aVoid -> {
                                            Log.d(TAG, "User data saved to Firestore successfully");
                                            Toast.makeText(activity, "Đăng ký thành công!", Toast.LENGTH_SHORT).show();
                                            navigateToLogin(activity);
                                        })
                                        .addOnFailureListener(e -> {
                                            Log.e(TAG, "Failed to save user data to Firestore", e);
                                            Toast.makeText(activity, "Lỗi lưu thông tin: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                                        });
                            } else {
                                Log.e(TAG, "Firebase user is null after successful registration");
                                Toast.makeText(activity, "Lỗi: Không thể lấy thông tin người dùng", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            Log.e(TAG, "Registration failed", task.getException());
                            String errorMessage = task.getException() != null ? task.getException().getMessage() : "Unknown error";
                            Toast.makeText(activity, "Đăng ký thất bại: " + errorMessage, Toast.LENGTH_LONG).show();
                        }
                    });
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Registration failed with exception", e);
                    activity.runOnUiThread(() -> {
                        Toast.makeText(activity, "Lỗi kết nối: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                });
    }

    // Login function
    public void signIn(Activity activity, String email, String password) {
        Log.d(TAG, "Starting login for email: " + email);

        // Show immediate feedback
        Toast.makeText(activity, "Đang đăng nhập...", Toast.LENGTH_SHORT).show();

        // Set timeout handler
        Handler timeoutHandler = new Handler(Looper.getMainLooper());
        final boolean[] callbackReceived = {false};

        Runnable timeoutRunnable = () -> {
            if (!callbackReceived[0]) {
                Log.e(TAG, "⏰ Login timeout after 10 seconds");
                Toast.makeText(activity, "⏰ Timeout: Vui lòng kiểm tra kết nối mạng", Toast.LENGTH_LONG).show();
            }
        };
        timeoutHandler.postDelayed(timeoutRunnable, 10000); // 10 seconds timeout

        auth.signInWithEmailAndPassword(email, password)
                .addOnCompleteListener(activity, task -> {
                    callbackReceived[0] = true;
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    Log.d(TAG, "Login task completed. Success: " + task.isSuccessful());

                    activity.runOnUiThread(() -> {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "Login successful");
                            Toast.makeText(activity, "Đăng nhập thành công!", Toast.LENGTH_SHORT).show();
                            navigateToMain(activity);
                        } else {
                            Log.e(TAG, "Login failed", task.getException());
                            String errorMessage = task.getException() != null ? task.getException().getMessage() : "Unknown error";
                            Toast.makeText(activity, "Đăng nhập thất bại: " + errorMessage, Toast.LENGTH_LONG).show();
                        }
                    });
                })
                .addOnFailureListener(e -> {
                    callbackReceived[0] = true;
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    Log.e(TAG, "Login failed with exception", e);
                    activity.runOnUiThread(() -> {
                        Toast.makeText(activity, "Lỗi kết nối: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                });
    }

    // Reset password function
    public void resetPassword(Activity activity, String email) {
        Log.d(TAG, "Starting password reset for email: " + email);

        auth.sendPasswordResetEmail(email)
                .addOnCompleteListener(task -> {
                    Log.d(TAG, "Password reset task completed. Success: " + task.isSuccessful());

                    activity.runOnUiThread(() -> {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "Password reset email sent successfully");
                            Toast.makeText(activity, "Link đặt lại mật khẩu đã được gửi đến email của bạn", Toast.LENGTH_LONG).show();
                        } else {
                            Log.e(TAG, "Password reset failed", task.getException());
                            String errorMessage = task.getException() != null ? task.getException().getMessage() : "Unknown error";
                            Toast.makeText(activity, "Có lỗi xảy ra: " + errorMessage, Toast.LENGTH_LONG).show();
                        }
                    });
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Password reset failed with exception", e);
                    activity.runOnUiThread(() -> {
                        Toast.makeText(activity, "Lỗi kết nối: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                });
    }

    public boolean isUserLoggedIn() {
        return auth.getCurrentUser() != null;
    }

    public void signOut(Activity activity) {
        Log.d(TAG, "Signing out user");
        auth.signOut();
        Toast.makeText(activity, "Đăng xuất thành công", Toast.LENGTH_SHORT).show();
        navigateToLogin(activity);
    }

    // Test method to check Firebase Auth
    public void testFirebaseAuth(Activity activity) {
        Log.d(TAG, "Testing Firebase Auth with simple test...");
        Toast.makeText(activity, "Testing Firebase Auth...", Toast.LENGTH_SHORT).show();

        // Set timeout handler for test
        Handler timeoutHandler = new Handler(Looper.getMainLooper());
        final boolean[] callbackReceived = {false};

        Runnable timeoutRunnable = () -> {
            if (!callbackReceived[0]) {
                Log.e(TAG, "⏰ Test Firebase Auth timeout after 10 seconds");
                Toast.makeText(activity, "⏰ Test Timeout: Firebase Auth không phản hồi", Toast.LENGTH_LONG).show();
            }
        };
        timeoutHandler.postDelayed(timeoutRunnable, 10000); // 10 seconds timeout

        // Try to sign in with a test account
        String testEmail = "<EMAIL>";
        String testPassword = "123456";

        auth.signInWithEmailAndPassword(testEmail, testPassword)
                .addOnCompleteListener(task -> {
                    callbackReceived[0] = true;
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    Log.d(TAG, "Test auth completed. Success: " + task.isSuccessful());

                    activity.runOnUiThread(() -> {
                        if (task.isSuccessful()) {
                            Toast.makeText(activity, "✅ Firebase Auth working!", Toast.LENGTH_LONG).show();
                            auth.signOut(); // Sign out immediately
                        } else {
                            // If test account doesn't exist, try to create it
                            Log.d(TAG, "Test account doesn't exist, creating...");
                            createTestAccount(activity, testEmail, testPassword);
                        }
                    });
                })
                .addOnFailureListener(e -> {
                    callbackReceived[0] = true;
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    Log.e(TAG, "Test auth failed", e);
                    activity.runOnUiThread(() -> {
                        Toast.makeText(activity, "❌ Firebase Auth error: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                });
    }

    private void createTestAccount(Activity activity, String email, String password) {
        Log.d(TAG, "Creating test account...");

        auth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(task -> {
                    Log.d(TAG, "Test account creation completed. Success: " + task.isSuccessful());

                    activity.runOnUiThread(() -> {
                        if (task.isSuccessful()) {
                            Toast.makeText(activity, "✅ Firebase Auth working! (Created test account)", Toast.LENGTH_LONG).show();
                            auth.signOut(); // Sign out immediately
                        } else {
                            String errorMessage = task.getException() != null ? task.getException().getMessage() : "Unknown error";
                            Toast.makeText(activity, "❌ Test account creation failed: " + errorMessage, Toast.LENGTH_LONG).show();
                        }
                    });
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Test account creation failed", e);
                    activity.runOnUiThread(() -> {
                        Toast.makeText(activity, "❌ Creation error: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                });
    }

    private void navigateToLogin(Activity activity) {
        Intent intent = new Intent(activity, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        activity.startActivity(intent);
        activity.finish();
    }

    private void navigateToMain(Activity activity) {
        Intent intent = new Intent(activity, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        activity.startActivity(intent);
        activity.finish();
    }
}
