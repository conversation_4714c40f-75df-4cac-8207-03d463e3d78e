<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Current Password Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_current_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Mật khẩu hiện tại"
            android:inputType="textPassword"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- New Password Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_new_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Mật khẩu mới"
            android:inputType="textPassword"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Confirm Password Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:boxStrokeColor="@color/primary_color"
        app:hintTextColor="@color/primary_color"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_confirm_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Xác nhận mật khẩu mới"
            android:inputType="textPassword"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
