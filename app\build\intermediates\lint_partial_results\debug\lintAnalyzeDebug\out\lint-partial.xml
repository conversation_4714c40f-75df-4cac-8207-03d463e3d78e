<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.11.1/transforms/9842d1e5e674fb9b92b55f2ec4220523/transformed/glide-4.11.0/jars/classes.jar"/>
        <entry
            name="className"
            string="com/bumptech/glide/request/target/NotificationTarget"/>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.onlinecoffeeshop.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.example.onlinecoffeeshop.view.auth.LoginActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.accent_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="10"
            column="12"
            startOffset="339"
            endLine="10"
            endColumn="31"
            endOffset="358"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="24"
            endOffset="76"/>
        <location id="R.color.error_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="823"
            endLine="24"
            endColumn="30"
            endOffset="841"/>
        <location id="R.color.info_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="919"
            endLine="26"
            endColumn="29"
            endOffset="936"/>
        <location id="R.color.success_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="774"
            endLine="23"
            endColumn="32"
            endOffset="794"/>
        <location id="R.color.surface_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="524"
            endLine="15"
            endColumn="32"
            endOffset="544"/>
        <location id="R.color.text_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="20"
            column="12"
            startOffset="699"
            endLine="20"
            endColumn="28"
            endOffset="715"/>
        <location id="R.color.warning_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="870"
            endLine="25"
            endColumn="32"
            endOffset="890"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="107"
            endLine="4"
            endColumn="24"
            endOffset="119"/>
        <location id="R.layout.activity_update_product"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_update_product.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="53"
            endOffset="483"/>
        <entry
            name="model"
            string="attr[actionBarSize(R)],color[card_background(U),primary_color(U),primary_dark(U),background_color(U),text_secondary(U),text_primary(U),secondary_color(U),black(D),white(D),accent_color(D),surface_color(D),text_hint(D),success_color(D),error_color(D),warning_color(D),info_color(D)],drawable[card_background(U),circle_background(U),ic_calendar(U),ic_coffee_logo(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_logout(U),ic_person(U),readonly_field_background(U)],id[edtName(U),edtCategoryId(U),edtDescription(U),edtPrice(U),edtImageUrl(U),chkIsActive(U),btnAction(U),iv_logo(U),tv_title(U),til_email(U),til_password(U),et_email(U),tv_forgot_password(U),et_password(U),btn_login(U),progress_bar(U),ll_register(U),tv_register(U),main(D),textView(U),btn_add(U),listProduct_btn(U),imgProduct(U),txtName(U),txtPrice(U),txtDescription(U),btnUpdate(U),btnAddToCart(U),recyclerProducts(U),toolbar(U),iv_avatar(U),tv_avatar_hint(U),card_user_info(U),tv_account_title(U),tv_email_label(U),tv_email(U),tv_role_label(U),tv_role(U),card_editable_info(U),tv_personal_title(U),til_fullname(U),et_fullname(U),til_dob(U),et_dob(U),til_address(U),et_address(U),til_phone(U),et_phone(U),btn_update_profile(U),btn_change_password(U),til_confirm_password(U),et_confirm_password(U),btn_register(U),ll_login(D),tv_login(U),et_current_password(U),et_new_password(U),btnDelete(U),action_logout(U)],layout[activity_add_product(U),activity_login(U),activity_main(U),activity_product_detail(U),activity_product_list(U),activity_profile(U),activity_register(U),activity_update_product(D),dialog_change_password(U),item_product(U)],menu[menu_profile(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U)],style[Theme_OnlineCoffeeShop(U),Theme_OnlineCoffeeShop_NoActionBar(U),ThemeOverlay_AppCompat_Dark_ActionBar(R),ThemeOverlay_AppCompat_Light(R),Base_Theme_OnlineCoffeeShop(U),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];11^1,12^2^3,13^2,14^2,16^17,18^2,57^4^14^23^2^24^22^25^27^29^2a^2b^5,58^2e^2f,5b^4^2^0^66^67^38^12^19^5^39^11^3a^3c^1a^6^3d^3e^3f^3b^42^43^13^45^47^41^7^4b^4c,5c^4^2^23^43^24^25^4d^13^45^47^49^4f^2a^5,5e^2,60^18,61^15^16,62^15^16,64^68,65^64,68^69;;;"/>
    </map>

</incidents>
