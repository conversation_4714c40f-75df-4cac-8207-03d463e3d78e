D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\model\User.java:45: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        this.role = role != null && VALID_ROLES.contains(role.toLowerCase()) ? role.toLowerCase() : "user";
                                                              ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\model\User.java:45: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        this.role = role != null && VALID_ROLES.contains(role.toLowerCase()) ? role.toLowerCase() : "user";
                                                                                    ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\model\User.java:56: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        this.role = role != null && VALID_ROLES.contains(role.toLowerCase()) ? role.toLowerCase() : "user";
                                                              ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\model\User.java:56: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        this.role = role != null && VALID_ROLES.contains(role.toLowerCase()) ? role.toLowerCase() : "user";
                                                                                    ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\model\User.java:100: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        this.role = role != null && VALID_ROLES.contains(role.toLowerCase()) ? role.toLowerCase() : "user";
                                                              ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\model\User.java:100: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        this.role = role != null && VALID_ROLES.contains(role.toLowerCase()) ? role.toLowerCase() : "user";
                                                                                    ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\repository\UserRepository.java:61: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        if (!User.VALID_ROLES.contains(role.toLowerCase())) {
                                            ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\repository\UserRepository.java:67: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                .update("role", role.toLowerCase());
                                     ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\repository\UserRepository.java:79: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        if (!User.VALID_ROLES.contains(role.toLowerCase())) {
                                            ~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\repository\UserRepository.java:84: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                .whereEqualTo("role", role.toLowerCase())
                                           ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml: Error: When targeting Android 13 or higher, posting a permission requires holding the POST_NOTIFICATIONS permission (usage from com.bumptech.glide.request.target.NotificationTarget) [NotificationPermission]

   Explanation for issues of type "NotificationPermission":
   When targeting Android 13 and higher, posting permissions requires holding
   the runtime permission android.permission.POST_NOTIFICATIONS.

D:\Project\CoffeeShop\Online-Coffee-Shop-\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.10.1 is available: 8.11.0 [AndroidGradlePluginVersion]
agp = "8.10.1"
      ~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:50: Warning: A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.15.0 [GradleDependency]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:59: Warning: A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0 [GradleDependency]
    implementation ("androidx.recyclerview:recyclerview:1.3.2")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:60: Warning: A newer version of com.github.bumptech.glide:glide than 4.11.0 is available: 4.12.0 [GradleDependency]
    implementation ("com.github.bumptech.glide:glide:4.11.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:61: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.6.2 is available: 2.9.1 [GradleDependency]
    implementation ("androidx.lifecycle:lifecycle-livedata-ktx:2.6.2")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:62: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.2 is available: 2.9.1 [GradleDependency]
    implementation ("androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\view\product\ProductListActivity.java:65: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                adapter.notifyDataSetChanged();
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:7: Warning: Possible overdraw: Root element paints background @color/background_color with a theme that also paints a background (inferred theme is @style/Theme_OnlineCoffeeShop_NoActionBar) [Overdraw]
    android:background="@color/background_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:6: Warning: Possible overdraw: Root element paints background @color/background_color with a theme that also paints a background (inferred theme is @style/Theme.OnlineCoffeeShop) [Overdraw]
    android:background="@color/background_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:6: Warning: Possible overdraw: Root element paints background @color/background_color with a theme that also paints a background (inferred theme is @style/Theme_OnlineCoffeeShop_NoActionBar) [Overdraw]
    android:background="@color/background_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_update_product.xml:2: Warning: The resource R.layout.activity_update_product appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:10: Warning: The resource R.color.accent_color appears to be unused [UnusedResources]
    <color name="accent_color">#CD853F</color>
           ~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.surface_color appears to be unused [UnusedResources]
    <color name="surface_color">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:20: Warning: The resource R.color.text_hint appears to be unused [UnusedResources]
    <color name="text_hint">#BDBDBD</color>
           ~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.success_color appears to be unused [UnusedResources]
    <color name="success_color">#4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.error_color appears to be unused [UnusedResources]
    <color name="error_color">#F44336</color>
           ~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.warning_color appears to be unused [UnusedResources]
    <color name="warning_color">#FF9800</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.info_color appears to be unused [UnusedResources]
    <color name="info_color">#2196F3</color>
           ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText android:id="@+id/edtName"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:16: Warning: This text field does not specify an inputType [TextFields]
        <EditText android:id="@+id/edtCategoryId"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:20: Warning: This text field does not specify an inputType [TextFields]
        <EditText android:id="@+id/edtDescription"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:31: Warning: This text field does not specify an inputType [TextFields]
        <EditText android:id="@+id/edtImageUrl"
         ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText android:id="@+id/edtName"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:16: Warning: Missing autofillHints attribute [Autofill]
        <EditText android:id="@+id/edtCategoryId"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:20: Warning: Missing autofillHints attribute [Autofill]
        <EditText android:id="@+id/edtDescription"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:25: Warning: Missing autofillHints attribute [Autofill]
        <EditText android:id="@+id/edtPrice"
         ~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:31: Warning: Missing autofillHints attribute [Autofill]
        <EditText android:id="@+id/edtImageUrl"
         ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:50: Warning: Use version catalog instead [UseTomlInstead]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:53: Warning: Use version catalog instead (com.google.firebase:firebase-auth is already available as firebase-auth, but using version 23.2.1 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-auth")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:54: Warning: Use version catalog instead (com.google.firebase:firebase-database is already available as firebase-database, but using version 21.0.0 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-database")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:55: Warning: Use version catalog instead (com.google.firebase:firebase-firestore is already available as firebase-firestore, but using version 25.1.4 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-firestore")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:56: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-storage")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:59: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("androidx.recyclerview:recyclerview:1.3.2")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:60: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.github.bumptech.glide:glide:4.11.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:61: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("androidx.lifecycle:lifecycle-livedata-ktx:2.6.2")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:62: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:63: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.squareup.retrofit2:retrofit:2.9.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build.gradle.kts:66: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.github.PhilJay:MPAndroidChart:3.1.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_product_detail.xml:11: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:34: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\item_product.xml:7: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:205: Warning: 'clickable' attribute found, please also add 'focusable' [KeyboardInaccessibleWidget]
                        android:clickable="true"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:136: Warning: 'clickable' attribute found, please also add 'focusable' [KeyboardInaccessibleWidget]
                android:clickable="true"
                ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "KeyboardInaccessibleWidget":
   A widget that is declared to be clickable but not declared to be focusable
   is not accessible via the keyboard. Please add the focusable attribute as
   well.

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\adapter\ProductAdapter.java:44: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.price.setText("$" + p.getPrice());
                             ~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\java\com\example\onlinecoffeeshop\view\product\ProductDetailActivity.java:55: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        txtPrice.setText("$" + price);
                         ~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:12: Warning: Hardcoded string "Product Name", should use @string resource [HardcodedText]
            android:hint="Product Name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:17: Warning: Hardcoded string "Category ID", should use @string resource [HardcodedText]
            android:hint="Category ID"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:21: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
            android:hint="Description"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:26: Warning: Hardcoded string "Price", should use @string resource [HardcodedText]
            android:hint="Price"
            ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:32: Warning: Hardcoded string "Image URL", should use @string resource [HardcodedText]
            android:hint="Image URL"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:38: Warning: Hardcoded string "Is Active", should use @string resource [HardcodedText]
            android:text="Is Active"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml:44: Warning: Hardcoded string "Add Product", should use @string resource [HardcodedText]
            android:text="Add Product"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:15: Warning: Hardcoded string "Coffee Shop Logo", should use @string resource [HardcodedText]
        android:contentDescription="Coffee Shop Logo"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:27: Warning: Hardcoded string "Đăng Nhập", should use @string resource [HardcodedText]
        android:text="Đăng Nhập"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:54: Warning: Hardcoded string "Email", should use @string resource [HardcodedText]
            android:hint="Email"
            ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:78: Warning: Hardcoded string "Mật khẩu", should use @string resource [HardcodedText]
            android:hint="Mật khẩu"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:89: Warning: Hardcoded string "Quên mật khẩu?", should use @string resource [HardcodedText]
        android:text="Quên mật khẩu?"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:106: Warning: Hardcoded string "Đăng Nhập", should use @string resource [HardcodedText]
        android:text="Đăng Nhập"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:144: Warning: Hardcoded string "Chưa có tài khoản? ", should use @string resource [HardcodedText]
            android:text="Chưa có tài khoản? "
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml:152: Warning: Hardcoded string "Đăng ký ngay", should use @string resource [HardcodedText]
            android:text="Đăng ký ngay"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_main.xml:25: Warning: Hardcoded string "Add Product", should use @string resource [HardcodedText]
        android:text="Add Product"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_main.xml:36: Warning: Hardcoded string "View Product List", should use @string resource [HardcodedText]
        android:text="View Product List"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_product_detail.xml:41: Warning: Hardcoded string "Update Product", should use @string resource [HardcodedText]
            android:text="Update Product"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_product_detail.xml:47: Warning: Hardcoded string "Add to Cart", should use @string resource [HardcodedText]
            android:text="Add to Cart"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:53: Warning: Hardcoded string "Chạm để thay đổi ảnh", should use @string resource [HardcodedText]
                android:text="Chạm để thay đổi ảnh"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:77: Warning: Hardcoded string "Thông tin tài khoản", should use @string resource [HardcodedText]
                    android:text="Thông tin tài khoản"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:89: Warning: Hardcoded string "Email", should use @string resource [HardcodedText]
                    android:text="Email"
                    ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:101: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                    android:text="<EMAIL>"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:116: Warning: Hardcoded string "Vai trò", should use @string resource [HardcodedText]
                    android:text="Vai trò"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:128: Warning: Hardcoded string "Khách hàng", should use @string resource [HardcodedText]
                    android:text="Khách hàng"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:157: Warning: Hardcoded string "Thông tin cá nhân", should use @string resource [HardcodedText]
                    android:text="Thông tin cá nhân"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:180: Warning: Hardcoded string "Họ và tên", should use @string resource [HardcodedText]
                        android:hint="Họ và tên"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:202: Warning: Hardcoded string "Ngày sinh (dd/mm/yyyy)", should use @string resource [HardcodedText]
                        android:hint="Ngày sinh (dd/mm/yyyy)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:228: Warning: Hardcoded string "Địa chỉ", should use @string resource [HardcodedText]
                        android:hint="Địa chỉ"
                        ~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:251: Warning: Hardcoded string "Số điện thoại", should use @string resource [HardcodedText]
                        android:hint="Số điện thoại"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:264: Warning: Hardcoded string "Cập nhật thông tin", should use @string resource [HardcodedText]
                android:text="Cập nhật thông tin"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml:278: Warning: Hardcoded string "Đổi mật khẩu", should use @string resource [HardcodedText]
                android:text="Đổi mật khẩu"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:18: Warning: Hardcoded string "Đăng Ký", should use @string resource [HardcodedText]
            android:text="Đăng Ký"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:43: Warning: Hardcoded string "Họ và tên", should use @string resource [HardcodedText]
                android:hint="Họ và tên"
                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:65: Warning: Hardcoded string "Email", should use @string resource [HardcodedText]
                android:hint="Email"
                ~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:88: Warning: Hardcoded string "Mật khẩu", should use @string resource [HardcodedText]
                android:hint="Mật khẩu"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:111: Warning: Hardcoded string "Xác nhận mật khẩu", should use @string resource [HardcodedText]
                android:hint="Xác nhận mật khẩu"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:133: Warning: Hardcoded string "Ngày sinh (dd/mm/yyyy)", should use @string resource [HardcodedText]
                android:hint="Ngày sinh (dd/mm/yyyy)"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:159: Warning: Hardcoded string "Địa chỉ", should use @string resource [HardcodedText]
                android:hint="Địa chỉ"
                ~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:181: Warning: Hardcoded string "Số điện thoại", should use @string resource [HardcodedText]
                android:hint="Số điện thoại"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:192: Warning: Hardcoded string "Đăng Ký", should use @string resource [HardcodedText]
            android:text="Đăng Ký"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:229: Warning: Hardcoded string "Đã có tài khoản? ", should use @string resource [HardcodedText]
                android:text="Đã có tài khoản? "
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml:237: Warning: Hardcoded string "Đăng nhập ngay", should use @string resource [HardcodedText]
                android:text="Đăng nhập ngay"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\dialog_change_password.xml:22: Warning: Hardcoded string "Mật khẩu hiện tại", should use @string resource [HardcodedText]
            android:hint="Mật khẩu hiện tại"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\dialog_change_password.xml:41: Warning: Hardcoded string "Mật khẩu mới", should use @string resource [HardcodedText]
            android:hint="Mật khẩu mới"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\dialog_change_password.xml:59: Warning: Hardcoded string "Xác nhận mật khẩu mới", should use @string resource [HardcodedText]
            android:hint="Xác nhận mật khẩu mới"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\item_product.xml:38: Warning: Hardcoded string "Xóa", should use @string resource [HardcodedText]
            android:text="Xóa"
            ~~~~~~~~~~~~~~~~~~
D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\menu\menu_profile.xml:7: Warning: Hardcoded string "Đăng xuất", should use @string resource [HardcodedText]
        android:title="Đăng xuất"
        ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

1 error, 105 warnings
