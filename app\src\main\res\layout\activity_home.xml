<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".view.home.HomeActivity">

    <!-- Scrollable Content -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottom_navigation">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp">

                <!-- User Avatar -->
                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_user_avatar"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/default_avatar"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <!-- User Name -->
                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:text="Tina Anderson"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@+id/iv_user_avatar"
                    app:layout_constraintEnd_toStartOf="@+id/iv_notification"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <!-- Notification Icon -->
                <ImageView
                    android:id="@+id/iv_notification"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/bg_icon_circle"
                    android:padding="8dp"
                    android:src="@drawable/ic_notification"
                    app:layout_constraintEnd_toStartOf="@+id/iv_filter"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <!-- Filter Icon -->
                <ImageView
                    android:id="@+id/iv_filter"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/bg_icon_circle"
                    android:padding="8dp"
                    android:src="@drawable/ic_filter"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Search Bar -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="25dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_search"
                        android:tint="@color/text_secondary" />

                    <EditText
                        android:id="@+id/et_search"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="Search anything"
                        android:textColorHint="@color/text_secondary"
                        android:textSize="16sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Promotion Banner -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cv_promotion"
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                android:backgroundTint="@color/coffee_brown">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="20dp">

                    <!-- Coffee Cup Image -->
                    <ImageView
                        android:id="@+id/iv_promo_coffee"
                        android:layout_width="80dp"
                        android:layout_height="100dp"
                        android:src="@drawable/coffee_cup_promo"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" />

                    <!-- Promo Text -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical"
                        app:layout_constraintStart_toEndOf="@+id/iv_promo_coffee"
                        app:layout_constraintEnd_toStartOf="@+id/btn_order_now"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Coffee Order Time 8:00 am - 3:00 pm"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Buy one,Get one\nfor Free"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Order Now Button -->
                    <Button
                        android:id="@+id/btn_order_now"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="Order Now"
                        android:textColor="@color/coffee_brown"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:background="@drawable/bg_button_white"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Category Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Category"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_categories"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    tools:listitem="@layout/item_category" />

            </LinearLayout>

            <!-- Popular Coffees Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Section Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Popular Coffees"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_see_all"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="See all"
                        android:textColor="@color/coffee_brown"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Products Grid -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_popular_coffees"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_coffee_product" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:menu="@menu/bottom_navigation_menu"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
