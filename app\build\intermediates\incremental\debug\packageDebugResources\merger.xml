<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res"><file name="card_background" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_coffee_logo" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\ic_coffee_logo.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_person" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="readonly_field_background" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\drawable\readonly_field_background.xml" qualifiers="" type="drawable"/><file name="activity_add_product" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_add_product.xml" qualifiers="" type="layout"/><file name="activity_login" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_product_detail" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_product_detail.xml" qualifiers="" type="layout"/><file name="activity_product_list" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_product_list.xml" qualifiers="" type="layout"/><file name="activity_profile" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_profile.xml" qualifiers="" type="layout"/><file name="activity_register" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_register.xml" qualifiers="" type="layout"/><file name="activity_update_product" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\activity_update_product.xml" qualifiers="" type="layout"/><file name="dialog_change_password" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\dialog_change_password.xml" qualifiers="" type="layout"/><file name="item_product" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\layout\item_product.xml" qualifiers="" type="layout"/><file name="menu_profile" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\menu\menu_profile.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#8B4513</color><color name="primary_dark">#5D2F0A</color><color name="secondary_color">#D2691E</color><color name="accent_color">#CD853F</color><color name="background_color">#F5F5F5</color><color name="card_background">#FFFFFF</color><color name="surface_color">#FFFFFF</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_hint">#BDBDBD</color><color name="success_color">#4CAF50</color><color name="error_color">#F44336</color><color name="warning_color">#FF9800</color><color name="info_color">#2196F3</color></file><file path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Online Coffee Shop</string></file><file path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.OnlineCoffeeShop" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.OnlineCoffeeShop" parent="Base.Theme.OnlineCoffeeShop"/><style name="Theme.OnlineCoffeeShop.NoActionBar" parent="Theme.OnlineCoffeeShop">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style></file><file path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.OnlineCoffeeShop" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build\generated\res\resValues\debug"/><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build\generated\res\resValues\debug"/><source path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build\generated\res\processDebugGoogleServices"><file path="D:\Project\CoffeeShop\Online-Coffee-Shop-\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">910256945534</string><string name="google_api_key" translatable="false">AIzaSyAUFaS6vuVLZovUQVE9kGFuby2uICLKOHE</string><string name="google_app_id" translatable="false">1:910256945534:android:a7f16ad1fedf2d270e1b2f</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAUFaS6vuVLZovUQVE9kGFuby2uICLKOHE</string><string name="google_storage_bucket" translatable="false">online-coffee-shop-3d7a2.firebasestorage.app</string><string name="project_id" translatable="false">online-coffee-shop-3d7a2</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>