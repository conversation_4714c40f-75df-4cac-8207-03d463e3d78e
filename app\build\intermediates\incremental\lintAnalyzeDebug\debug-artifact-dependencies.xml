<dependencies>
  <compile
      roots="com.google.android.material:material:1.12.0@aar,androidx.appcompat:appcompat-resources:1.7.1@aar,androidx.appcompat:appcompat:1.7.1@aar,com.google.firebase:firebase-auth:22.3.0@aar,com.github.bumptech.glide:glide:4.11.0@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.credentials:credentials-play-services-auth:1.5.0@aar,com.google.android.libraries.identity.googleid:googleid:1.1.1@aar,androidx.credentials:credentials:1.5.0@aar,androidx.credentials:credentials:1.5.0@aar,androidx.biometric:biometric:1.1.0@aar,com.google.firebase:firebase-database:20.3.0@aar,com.google.firebase:firebase-firestore:24.10.0@aar,com.google.firebase:firebase-storage:20.3.0@aar,com.google.firebase:firebase-appcheck:17.1.1@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common:20.4.2@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.recaptcha:recaptcha:18.1.2@aar,com.google.android.play:integrity:1.1.0@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.core:core-ktx:1.15.0@aar,androidx.browser:browser:1.4.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.15.0@aar,androidx.core:core:1.15.0@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.5.7@aar,androidx.fragment:fragment:1.5.7@aar,androidx.activity:activity:1.10.1@aar,androidx.constraintlayout:constraintlayout:2.2.1@aar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.github.PhilJay:MPAndroidChart:3.1.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.cardview:cardview:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,com.google.firebase:firebase-components:17.1.5@aar,com.github.bumptech.glide:gifdecoder:4.11.0@aar,androidx.exifinterface:exifinterface:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains:annotations:23.0.0@jar,io.grpc:grpc-stub:1.52.1@jar,com.google.guava:guava:31.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,io.grpc:grpc-protobuf-lite:1.52.1@jar,io.grpc:grpc-android:1.52.1@aar,io.grpc:grpc-okhttp:1.52.1@jar,io.grpc:grpc-core:1.52.1@jar,io.grpc:grpc-api:1.52.1@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,io.grpc:grpc-context:1.52.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar,com.squareup.okhttp3:okhttp:3.14.9@jar,com.squareup.okio:okio:1.17.5@jar,com.google.firebase:protolite-well-known-types:18.0.0@aar,com.google.protobuf:protobuf-javalite:3.21.7@jar,com.github.bumptech.glide:disklrucache:4.11.0@jar,com.github.bumptech.glide:annotations:4.11.0@jar">
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.firebase:firebase-auth:22.3.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.11.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.5.0@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.1@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="androidx.credentials:credentials:1.5.0@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="com.google.firebase:firebase-database:20.3.0@aar"
        simpleName="com.google.firebase:firebase-database"/>
    <dependency
        name="com.google.firebase:firebase-firestore:24.10.0@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name="com.google.firebase:firebase-storage:20.3.0@aar"
        simpleName="com.google.firebase:firebase-storage"/>
    <dependency
        name="com.google.firebase:firebase-appcheck:17.1.1@aar"
        simpleName="com.google.firebase:firebase-appcheck"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.1.2@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.play:integrity:1.1.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.15.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.browser:browser:1.4.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.15.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.11.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.0.0@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="io.grpc:grpc-stub:1.52.1@jar"
        simpleName="io.grpc:grpc-stub"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="io.grpc:grpc-protobuf-lite:1.52.1@jar"
        simpleName="io.grpc:grpc-protobuf-lite"/>
    <dependency
        name="io.grpc:grpc-android:1.52.1@aar"
        simpleName="io.grpc:grpc-android"/>
    <dependency
        name="io.grpc:grpc-okhttp:1.52.1@jar"
        simpleName="io.grpc:grpc-okhttp"/>
    <dependency
        name="io.grpc:grpc-core:1.52.1@jar"
        simpleName="io.grpc:grpc-core"/>
    <dependency
        name="io.grpc:grpc-api:1.52.1@jar"
        simpleName="io.grpc:grpc-api"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="io.grpc:grpc-context:1.52.1@jar"
        simpleName="io.grpc:grpc-context"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.14.9@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:1.17.5@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.21.7@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.11.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.11.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
  </compile>
  <package
      roots="com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.2.1@aar,androidx.appcompat:appcompat-resources:1.7.1@aar,androidx.credentials:credentials-play-services-auth:1.5.0@aar,com.google.android.libraries.identity.googleid:googleid:1.1.1@aar,androidx.credentials:credentials:1.5.0@aar,androidx.credentials:credentials:1.5.0@aar,androidx.biometric:biometric:1.1.0@aar,androidx.appcompat:appcompat:1.7.1@aar,com.github.bumptech.glide:glide:4.11.0@aar,com.google.firebase:firebase-auth:22.3.0@aar,com.google.android.gms:play-services-auth:21.1.1@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,com.google.firebase:firebase-firestore:24.10.0@aar,com.google.firebase:firebase-storage:20.3.0@aar,com.google.firebase:firebase-database:20.3.0@aar,com.google.android.gms:play-services-auth-blockstore:16.4.0@aar,com.google.android.gms:play-services-fido:21.0.0@aar,com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.firebase:firebase-appcheck:17.1.1@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.recaptcha:recaptcha:18.1.2@aar,com.google.android.play:integrity:1.1.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-common:20.4.2@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.browser:browser:1.4.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.15.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.15.0@aar,androidx.core:core:1.15.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.5.7@aar,androidx.fragment:fragment:1.5.7@aar,androidx.activity:activity:1.10.1@aar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.github.PhilJay:MPAndroidChart:3.1.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.constraintlayout:constraintlayout-core:1.1.1@jar,com.github.bumptech.glide:gifdecoder:4.11.0@aar,androidx.exifinterface:exifinterface:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,com.google.firebase:firebase-components:17.1.5@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.tracing:tracing:1.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.collection:collection-jvm:1.4.2@jar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,io.grpc:grpc-stub:1.52.1@jar,io.grpc:grpc-android:1.52.1@aar,io.grpc:grpc-okhttp:1.52.1@jar,io.grpc:grpc-core:1.52.1@jar,io.grpc:grpc-protobuf-lite:1.52.1@jar,io.grpc:grpc-api:1.52.1@jar,com.google.guava:guava:31.1-android@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,com.github.bumptech.glide:disklrucache:4.11.0@jar,com.github.bumptech.glide:annotations:4.11.0@jar,com.squareup.okhttp3:okhttp:3.14.9@jar,com.google.firebase:firebase-annotations:16.2.0@jar,com.google.firebase:protolite-well-known-types:18.0.0@aar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.jetbrains:annotations:23.0.0@jar,com.squareup.okio:okio:1.17.5@jar,javax.inject:javax.inject:1@jar,io.perfmark:perfmark-api:0.25.0@jar,com.google.protobuf:protobuf-javalite:3.21.7@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.code.gson:gson:2.9.0@jar,com.google.android:annotations:4.1.1.4@jar,org.codehaus.mojo:animal-sniffer-annotations:1.21@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar,io.grpc:grpc-context:1.52.1@jar">
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.5.0@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.1@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="androidx.credentials:credentials:1.5.0@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.11.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.firebase:firebase-auth:22.3.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.1.1@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.firebase:firebase-firestore:24.10.0@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name="com.google.firebase:firebase-storage:20.3.0@aar"
        simpleName="com.google.firebase:firebase-storage"/>
    <dependency
        name="com.google.firebase:firebase-database:20.3.0@aar"
        simpleName="com.google.firebase:firebase-database"/>
    <dependency
        name="com.google.android.gms:play-services-auth-blockstore:16.4.0@aar"
        simpleName="com.google.android.gms:play-services-auth-blockstore"/>
    <dependency
        name="com.google.android.gms:play-services-fido:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02@aar"
        simpleName="com.google.android.gms:play-services-identity-credentials"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.firebase:firebase-appcheck:17.1.1@aar"
        simpleName="com.google.firebase:firebase-appcheck"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.1.2@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.play:integrity:1.1.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.browser:browser:1.4.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.15.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.15.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.11.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.0.0@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.grpc:grpc-stub:1.52.1@jar"
        simpleName="io.grpc:grpc-stub"/>
    <dependency
        name="io.grpc:grpc-android:1.52.1@aar"
        simpleName="io.grpc:grpc-android"/>
    <dependency
        name="io.grpc:grpc-okhttp:1.52.1@jar"
        simpleName="io.grpc:grpc-okhttp"/>
    <dependency
        name="io.grpc:grpc-core:1.52.1@jar"
        simpleName="io.grpc:grpc-core"/>
    <dependency
        name="io.grpc:grpc-protobuf-lite:1.52.1@jar"
        simpleName="io.grpc:grpc-protobuf-lite"/>
    <dependency
        name="io.grpc:grpc-api:1.52.1@jar"
        simpleName="io.grpc:grpc-api"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.11.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.11.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.14.9@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.squareup.okio:okio:1.17.5@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="io.perfmark:perfmark-api:0.25.0@jar"
        simpleName="io.perfmark:perfmark-api"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.21.7@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.code.gson:gson:2.9.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.android:annotations:4.1.1.4@jar"
        simpleName="com.google.android:annotations"/>
    <dependency
        name="org.codehaus.mojo:animal-sniffer-annotations:1.21@jar"
        simpleName="org.codehaus.mojo:animal-sniffer-annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="io.grpc:grpc-context:1.52.1@jar"
        simpleName="io.grpc:grpc-context"/>
  </package>
</dependencies>
