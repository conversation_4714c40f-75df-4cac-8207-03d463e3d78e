-- Merging decision tree log ---
manifest
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:2:1-69:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6ad1e95c77cb6c889557260421a538\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:17:1-52:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7666bde154852e3b9810060b3a953de2\transformed\googleid-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3deef03585fdbcf847060ea76ca8493b\transformed\credentials-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab96aaedc8b77be230de629d335c4682\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8685b6352ddfcf9d146fa7e0dbe85eda\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\412e880df672c62ce7808924cfdc49c9\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91267986d86052c131647de2dab9bd8\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\226ff83f876b7de3946186fb79c709f8\transformed\play-services-fido-21.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7298d5e8da23f1d0a77753a81bc244\transformed\play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f79c63c90a2a5b4f2db8cd747ff45c81\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ae541c50319bb2e5eb82d03a1ce67ec\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f6f5f82ac32c09b1166061c1c3ef5df\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201989e88fc2ce9268d5ee71ec56015a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32fe6f491d0c652ec40c99e8c355842c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\425055bd0a74b18769344c92bfddf926\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5418308e7f3d601ff183d1d3ef2a0b64\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02600de27416b9134e507fddbbfbd334\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea628c60eede608044342426c52a971\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3247c3f3054ee99772ccb09043368b74\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfdf836f4031be9c36703d49bea6653b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\370dc79520a49ee02f6105a4c76f3d16\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\123ac6967623a4f97269f1053d7be3bd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a1efbb09df5a90a9fe4df37d0a39528\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a850213f3e23b3f6e5a5b98f759110a\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f2b241146d22db375d6b4f8aaceca8\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb079a091241efb15a7df9b94ad1c219\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c0616fa7757653de67b32f10aea4bb8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d621dd94924f45342c44c4da4705ad\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b95241c520638c422155228b404983f1\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b81770157f9e38f553bbc16749e2cf6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4aff8f7ec86942f01c117df75cf193b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7baaf667d83bcc7fe6b2e65137ed0cae\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2c48b8b5d96d703fab7340ac903d3ab\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e0d5a43066a07cdf2c570e81caae6d1\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc9708d53e8f2793bb83dda03f7f198b\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9bfda81c72d91dd55ac3f5c969251\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\611e13657beea8829ad9c039ab6893fe\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\288e392541398d1d10d51ea56470994b\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80717a733d614bb44cb510a2438dabac\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.PhilJay:MPAndroidChart:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c71d6b2c8bf4daf1d844d82d0aad70\transformed\MPAndroidChart-3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd11c8c0fb69258ad9d0e9ecb212d4c3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac941a4f9bf2b621bd49b16708814fce\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ce7d0903b72d5587c01aaba5a30065b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7737732c55951be30631b3d74c380a8\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098d10f6ec73f19d9d936d0d2332d328\transformed\exifinterface-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e66153af1fc7957406d639b1ef7f6ee\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ef4e809395bc4486a9b6fe6fa7b1dba\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1681298b919f57702070e3c063367446\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75e67f47b574ded484ed55a4033468cb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c993e987cfaf65ee58de96a2342808\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1950c847e3cf941e297593cba6b0a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12712e705918a79ddd5f948f794d63a2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\447080d16b933adc4db8f337578206cc\transformed\grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b282464bbde9a367475a2c8cb2f1de02\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\447080d16b933adc4db8f337578206cc\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\447080d16b933adc4db8f337578206cc\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:5-67:19
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:9:5-67:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:23:5-50:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:23:5-50:19
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0\AndroidManifest.xml:10:5-20
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\412e880df672c62ce7808924cfdc49c9\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\412e880df672c62ce7808924cfdc49c9\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91267986d86052c131647de2dab9bd8\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91267986d86052c131647de2dab9bd8\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\226ff83f876b7de3946186fb79c709f8\transformed\play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\226ff83f876b7de3946186fb79c709f8\transformed\play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\611e13657beea8829ad9c039ab6893fe\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\611e13657beea8829ad9c039ab6893fe\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:15:9-54
	tools:targetApi
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:18:9-29
	android:icon
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:17:9-54
	android:dataExtractionRules
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:11:9-65
activity#com.example.onlinecoffeeshop.view.auth.LoginActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:20:9-29:20
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:23:13-70
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:21:13-52
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:24:14-28:30
action#android.intent.action.MAIN
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:18-70
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:25:26-67
category#android.intent.category.LAUNCHER
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:18-78
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:27:28-75
activity#com.example.onlinecoffeeshop.view.auth.RegisterActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:30:9-33:73
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:32:13-37
	android:theme
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:33:13-70
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:31:13-55
activity#com.example.onlinecoffeeshop.view.profile.ProfileActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:36:9-39:58
	android:parentActivityName
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:39:13-55
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:38:13-37
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:37:13-57
activity#com.example.onlinecoffeeshop.debug.FirestoreTestActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:42:9-45:61
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:44:13-37
	android:theme
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:45:13-58
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:43:13-56
activity#com.example.onlinecoffeeshop.view.product.UpdateProductActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:49:13-63
activity#com.example.onlinecoffeeshop.view.product.ProductDetailActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:51:9-53:40
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:52:13-63
activity#com.example.onlinecoffeeshop.view.product.ProductListActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:54:9-56:40
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:55:13-61
activity#com.example.onlinecoffeeshop.view.product.AddProductActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:57:9-59:40
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:59:13-37
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:58:13-60
activity#com.example.onlinecoffeeshop.MainActivity
ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:62:9-66:20
	android:exported
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:64:13-36
	android:name
		ADDED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml:63:13-41
uses-sdk
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\927157856c8a68fcd4c771cacd883cfc\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c080fd7e4c2108fabcc4bb48cd12bb\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6ad1e95c77cb6c889557260421a538\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6ad1e95c77cb6c889557260421a538\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7666bde154852e3b9810060b3a953de2\transformed\googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7666bde154852e3b9810060b3a953de2\transformed\googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3deef03585fdbcf847060ea76ca8493b\transformed\credentials-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3deef03585fdbcf847060ea76ca8493b\transformed\credentials-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab96aaedc8b77be230de629d335c4682\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab96aaedc8b77be230de629d335c4682\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9842d1e5e674fb9b92b55f2ec4220523\transformed\glide-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8685b6352ddfcf9d146fa7e0dbe85eda\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8685b6352ddfcf9d146fa7e0dbe85eda\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\412e880df672c62ce7808924cfdc49c9\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\412e880df672c62ce7808924cfdc49c9\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7293d53f86294d80fbd6338fb08d19b5\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91267986d86052c131647de2dab9bd8\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91267986d86052c131647de2dab9bd8\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\226ff83f876b7de3946186fb79c709f8\transformed\play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\226ff83f876b7de3946186fb79c709f8\transformed\play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7298d5e8da23f1d0a77753a81bc244\transformed\play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7298d5e8da23f1d0a77753a81bc244\transformed\play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20b2b7bb3559356476410afdfe5f93c0\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f79c63c90a2a5b4f2db8cd747ff45c81\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f79c63c90a2a5b4f2db8cd747ff45c81\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ae541c50319bb2e5eb82d03a1ce67ec\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ae541c50319bb2e5eb82d03a1ce67ec\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f6f5f82ac32c09b1166061c1c3ef5df\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f6f5f82ac32c09b1166061c1c3ef5df\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201989e88fc2ce9268d5ee71ec56015a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201989e88fc2ce9268d5ee71ec56015a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32fe6f491d0c652ec40c99e8c355842c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32fe6f491d0c652ec40c99e8c355842c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\425055bd0a74b18769344c92bfddf926\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\425055bd0a74b18769344c92bfddf926\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5418308e7f3d601ff183d1d3ef2a0b64\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5418308e7f3d601ff183d1d3ef2a0b64\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02600de27416b9134e507fddbbfbd334\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02600de27416b9134e507fddbbfbd334\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea628c60eede608044342426c52a971\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea628c60eede608044342426c52a971\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3247c3f3054ee99772ccb09043368b74\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3247c3f3054ee99772ccb09043368b74\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfdf836f4031be9c36703d49bea6653b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfdf836f4031be9c36703d49bea6653b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\370dc79520a49ee02f6105a4c76f3d16\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\370dc79520a49ee02f6105a4c76f3d16\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\123ac6967623a4f97269f1053d7be3bd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\123ac6967623a4f97269f1053d7be3bd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a1efbb09df5a90a9fe4df37d0a39528\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a1efbb09df5a90a9fe4df37d0a39528\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a850213f3e23b3f6e5a5b98f759110a\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a850213f3e23b3f6e5a5b98f759110a\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f2b241146d22db375d6b4f8aaceca8\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f2b241146d22db375d6b4f8aaceca8\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb079a091241efb15a7df9b94ad1c219\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb079a091241efb15a7df9b94ad1c219\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c0616fa7757653de67b32f10aea4bb8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c0616fa7757653de67b32f10aea4bb8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d621dd94924f45342c44c4da4705ad\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d621dd94924f45342c44c4da4705ad\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b95241c520638c422155228b404983f1\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b95241c520638c422155228b404983f1\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b81770157f9e38f553bbc16749e2cf6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b81770157f9e38f553bbc16749e2cf6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4aff8f7ec86942f01c117df75cf193b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4aff8f7ec86942f01c117df75cf193b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7baaf667d83bcc7fe6b2e65137ed0cae\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7baaf667d83bcc7fe6b2e65137ed0cae\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2c48b8b5d96d703fab7340ac903d3ab\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2c48b8b5d96d703fab7340ac903d3ab\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e0d5a43066a07cdf2c570e81caae6d1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e0d5a43066a07cdf2c570e81caae6d1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc9708d53e8f2793bb83dda03f7f198b\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc9708d53e8f2793bb83dda03f7f198b\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9bfda81c72d91dd55ac3f5c969251\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9bfda81c72d91dd55ac3f5c969251\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\611e13657beea8829ad9c039ab6893fe\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\611e13657beea8829ad9c039ab6893fe\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\288e392541398d1d10d51ea56470994b\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\288e392541398d1d10d51ea56470994b\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80717a733d614bb44cb510a2438dabac\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80717a733d614bb44cb510a2438dabac\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c71d6b2c8bf4daf1d844d82d0aad70\transformed\MPAndroidChart-3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95c71d6b2c8bf4daf1d844d82d0aad70\transformed\MPAndroidChart-3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd11c8c0fb69258ad9d0e9ecb212d4c3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd11c8c0fb69258ad9d0e9ecb212d4c3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac941a4f9bf2b621bd49b16708814fce\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac941a4f9bf2b621bd49b16708814fce\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ce7d0903b72d5587c01aaba5a30065b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ce7d0903b72d5587c01aaba5a30065b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7737732c55951be30631b3d74c380a8\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7737732c55951be30631b3d74c380a8\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a73e6d53f358e019e6ba7222f4ed6871\transformed\gifdecoder-4.11.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098d10f6ec73f19d9d936d0d2332d328\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098d10f6ec73f19d9d936d0d2332d328\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e66153af1fc7957406d639b1ef7f6ee\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e66153af1fc7957406d639b1ef7f6ee\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ef4e809395bc4486a9b6fe6fa7b1dba\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ef4e809395bc4486a9b6fe6fa7b1dba\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4ee00bde0980685d9e3a26d80625b32\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1681298b919f57702070e3c063367446\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1681298b919f57702070e3c063367446\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75e67f47b574ded484ed55a4033468cb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75e67f47b574ded484ed55a4033468cb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c993e987cfaf65ee58de96a2342808\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c993e987cfaf65ee58de96a2342808\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1950c847e3cf941e297593cba6b0a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1950c847e3cf941e297593cba6b0a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12712e705918a79ddd5f948f794d63a2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12712e705918a79ddd5f948f794d63a2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\447080d16b933adc4db8f337578206cc\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\447080d16b933adc4db8f337578206cc\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b282464bbde9a367475a2c8cb2f1de02\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b282464bbde9a367475a2c8cb2f1de02\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Project\CoffeeShop\Online-Coffee-Shop-\app\src\main\AndroidManifest.xml
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3575c6eabe91adb80925594d42a6b9f\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
activity#androidx.credentials.playservices.IdentityCredentialApiHiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d693b382f5f3523c6ba333d9f8110c3\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e6cd48de5ab9a20fa97ed71f349c2a\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fa13aee40c9f65cbcae266cbb39a3\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac20f6c8d603d953258a5defe11c02e\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e18734415571511663e33983b47e005\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a62f1bea2d9352bb907909e8d544217a\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92f5f4427b1a039ece628bdab07be6c5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8684cacb6a364a4a26bf3239378f6899\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc5af35b0d09b571bfa353c42007aca8\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab124b9b1cd4957497c9c203960b4f1\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3de113f6dce269c2b16bddad99a47d3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d16d4897132c572d134f82ed4c8262\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6652059f755addc44c2f11488007a2c1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.onlinecoffeeshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da31a2cc9a105a6c76a8d863f14fb806\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e811b4fefd2195034d3a31bba55d5bd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff6c67585aec0bbe59340a829427f899\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d81e5298f3460a7b1164ba49035286a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab7118a4b491c02228c5654d158e7e8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
