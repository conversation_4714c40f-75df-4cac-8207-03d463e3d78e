import com.example.onlinecoffeeshop.controller.AuthController;
import com.example.onlinecoffeeshop.model.User;

/**
 * Simple test class to verify registration flow
 * This is a standalone test to check if the AuthController registration works correctly
 */
public class TestRegistration {
    
    public static void main(String[] args) {
        System.out.println("=== Testing Registration Flow ===");
        
        // Create test user data
        User testUser = new User();
        testUser.setFullname("Test User");
        testUser.setDob("01/01/1990");
        testUser.setAddress("123 Test Street");
        testUser.setPhone("0123456789");
        testUser.setRole("user"); // Default role
        
        // Create AuthController
        AuthController authController = new AuthController();
        
        // Test email and password
        String testEmail = "testuser" + System.currentTimeMillis() + "@example.com";
        String testPassword = "password123";
        
        System.out.println("Test Email: " + testEmail);
        System.out.println("Test Password: " + testPassword);
        System.out.println("Test User Data: " + testUser.toString());
        
        // Test registration
        authController.registerUser(testEmail, testPassword, testUser, new AuthController.AuthCallback() {
            @Override
            public void onSuccess() {
                System.out.println("✅ REGISTRATION SUCCESS!");
                System.out.println("User should be created in Firebase Auth and Firestore");
                
                // Test login with the same credentials
                testLogin(authController, testEmail, testPassword);
            }
            
            @Override
            public void onFailure(String error) {
                System.out.println("❌ REGISTRATION FAILED: " + error);
                System.exit(1);
            }
        });
        
        // Keep the program running to wait for async operations
        try {
            Thread.sleep(10000); // Wait 10 seconds for operations to complete
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    
    private static void testLogin(AuthController authController, String email, String password) {
        System.out.println("\n=== Testing Login Flow ===");
        
        authController.loginUser(email, password, new AuthController.AuthCallback() {
            @Override
            public void onSuccess() {
                System.out.println("✅ LOGIN SUCCESS!");
                
                // Test getting current user data
                testGetCurrentUser(authController);
            }
            
            @Override
            public void onFailure(String error) {
                System.out.println("❌ LOGIN FAILED: " + error);
            }
        });
    }
    
    private static void testGetCurrentUser(AuthController authController) {
        System.out.println("\n=== Testing Get Current User ===");
        
        authController.getCurrentUser(new AuthController.UserCallback() {
            @Override
            public void onSuccess(java.util.Map<String, Object> userData) {
                System.out.println("✅ GET USER DATA SUCCESS!");
                System.out.println("User Data from Firestore:");
                for (java.util.Map.Entry<String, Object> entry : userData.entrySet()) {
                    System.out.println("  " + entry.getKey() + ": " + entry.getValue());
                }
                
                // Verify required fields
                String[] requiredFields = {"uid", "fullname", "dob", "address", "phone", "role", "email"};
                boolean allFieldsPresent = true;
                
                for (String field : requiredFields) {
                    if (!userData.containsKey(field) || userData.get(field) == null) {
                        System.out.println("❌ Missing required field: " + field);
                        allFieldsPresent = false;
                    }
                }
                
                if (allFieldsPresent) {
                    System.out.println("✅ ALL REQUIRED FIELDS PRESENT!");
                    System.out.println("🎉 REGISTRATION FLOW TEST COMPLETED SUCCESSFULLY!");
                } else {
                    System.out.println("❌ SOME REQUIRED FIELDS ARE MISSING!");
                }
                
                System.exit(0);
            }
            
            @Override
            public void onFailure(String error) {
                System.out.println("❌ GET USER DATA FAILED: " + error);
                System.exit(1);
            }
        });
    }
}
