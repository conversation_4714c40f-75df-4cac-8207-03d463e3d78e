<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AppInsightsSettings">
    <option name="tabSettings">
      <map>
        <entry key="Firebase Crashlytics">
          <value>
            <InsightsFilterSettings>
              <option name="connection">
                <ConnectionSetting>
                  <option name="appId" value="com.example.onlinecoffeeshop" />
                  <option name="mobileSdkAppId" value="1:910256945534:android:a7f16ad1fedf2d270e1b2f" />
                  <option name="projectId" value="online-coffee-shop-3d7a2" />
                  <option name="projectNumber" value="910256945534" />
                </ConnectionSetting>
              </option>
              <option name="signal" value="SIGNAL_UNSPECIFIED" />
              <option name="timeIntervalDays" value="THIRTY_DAYS" />
              <option name="visibilityType" value="ALL" />
            </InsightsFilterSettings>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>