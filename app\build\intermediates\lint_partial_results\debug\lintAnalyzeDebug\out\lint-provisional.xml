<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_coffee_logo.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_coffee_logo.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_logout.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_logout.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_person.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_person.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="756"
                endOffset="776"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="18"
            column="9"
            startOffset="756"
            endLine="18"
            endColumn="29"
            endOffset="776"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

    <incident
        id="KeyboardInaccessibleWidget"
        severity="warning"
        message="&apos;clickable&apos; attribute found, please also add &apos;focusable&apos;">
        <fix-attribute
            description="Set focusable=&quot;true&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="focusable"
            value="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="205"
            column="25"
            startOffset="9910"
            endLine="205"
            endColumn="49"
            endOffset="9934"/>
        <map>
            <condition minLT="26-∞"/>
        </map>
    </incident>

    <incident
        id="KeyboardInaccessibleWidget"
        severity="warning"
        message="&apos;clickable&apos; attribute found, please also add &apos;focusable&apos;">
        <fix-attribute
            description="Set focusable=&quot;true&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="focusable"
            value="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_register.xml"
            line="136"
            column="17"
            startOffset="5986"
            endLine="136"
            endColumn="41"
            endOffset="6010"/>
        <map>
            <condition minLT="26-∞"/>
        </map>
    </incident>

</incidents>
