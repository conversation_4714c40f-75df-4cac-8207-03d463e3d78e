package com.example.onlinecoffeeshop.debug;

import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.onlinecoffeeshop.R;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;

public class FirebaseTestActivity extends AppCompatActivity {
    private static final String TAG = "FirebaseTestActivity";
    
    private FirebaseAuth auth;
    private FirebaseFirestore firestore;
    private Button btnTestConnection;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main); // Using existing layout for now
        
        Log.d(TAG, "FirebaseTestActivity created");
        
        // Initialize Firebase
        auth = FirebaseAuth.getInstance();
        firestore = FirebaseFirestore.getInstance();
        
        // Test Firebase connection
        testFirebaseConnection();
    }
    
    private void testFirebaseConnection() {
        Log.d(TAG, "Testing Firebase connection...");
        
        // Test Auth
        if (auth != null) {
            Log.d(TAG, "Firebase Auth initialized successfully");
            Toast.makeText(this, "Firebase Auth: OK", Toast.LENGTH_SHORT).show();
        } else {
            Log.e(TAG, "Firebase Auth is null");
            Toast.makeText(this, "Firebase Auth: FAILED", Toast.LENGTH_SHORT).show();
        }
        
        // Test Firestore
        if (firestore != null) {
            Log.d(TAG, "Firebase Firestore initialized successfully");
            Toast.makeText(this, "Firebase Firestore: OK", Toast.LENGTH_SHORT).show();
            
            // Try to access Firestore
            firestore.collection("test")
                    .document("connection")
                    .get()
                    .addOnSuccessListener(documentSnapshot -> {
                        Log.d(TAG, "Firestore connection test successful");
                        Toast.makeText(this, "Firestore Connection: OK", Toast.LENGTH_SHORT).show();
                    })
                    .addOnFailureListener(e -> {
                        Log.e(TAG, "Firestore connection test failed", e);
                        Toast.makeText(this, "Firestore Connection: FAILED - " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
        } else {
            Log.e(TAG, "Firebase Firestore is null");
            Toast.makeText(this, "Firebase Firestore: FAILED", Toast.LENGTH_SHORT).show();
        }
    }
}
